<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Google Sign-In Test</title>
  <script src="https://accounts.google.com/gsi/client" async defer></script>
  <style>
    .google-btn {
      background-color: white;
      border: 1px solid #dadce0;
      border-radius: 4px;
      padding: 10px 16px;
      font-size: 16px;
      color: #3c4043;
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    .google-btn:hover {
      background-color: #f7f7f7;
    }
    .google-icon {
      height: 20px;
      margin-right: 8px;
    }
  </style>
</head>
<body>
  <h2>Custom Google Sign-In</h2>
  <div id="customBtn" class="google-btn">
    <img class="google-icon" src="https://developers.google.com/identity/images/g-logo.png" alt="Google logo"/>
    <span>Sign in with Google</span>
  </div>

 <script>
	const clientId = '*************-vc8tfhabulq8u72olf2449kckfk9cm85.apps.googleusercontent.com';

  window.onload = function () {
    google.accounts.id.initialize({
      client_id: clientId,
      callback: handleCredentialResponse
    });

    // Renders the default Google button in the custom button div
    google.accounts.id.renderButton(
      document.getElementById('customBtn'),
      { theme: 'outline', size: 'large', text: 'signin_with' }
    );

    // Optional: disable One Tap to avoid FedCM issues
    google.accounts.id.disableAutoSelect();
  };

  function handleCredentialResponse(response) {
    const idToken = response.credential;
    console.log("ID Token:", idToken);

    fetch('https://api.sagecloud-core.dev.ercaspay.com/api/v1/auth/google/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ id_token: idToken })
    })
    .then(res => res.json())
    .then(data => console.log("Backend Response:", data))
    .catch(err => console.error("Error:", err));
  }
</script>
</body>
</html>
