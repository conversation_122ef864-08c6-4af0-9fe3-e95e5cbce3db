from console.models import SystemVASProduct
from django.core.management.base import BaseCommand
from transaction.enums import TransactionClassEnum


class Command(BaseCommand):
    help = "Seeds the database with all System VAS Products from TransactionClassEnum."

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting to seed System VAS Products..."))

        created_count = 0
        skipped_count = 0

        for transaction_type, _ in TransactionClassEnum.choices():
            obj, created = SystemVASProduct.objects.get_or_create(
                type=transaction_type,
                defaults={
                    "is_active": True,
                    # activated_at will be set automatically by auto_now_add
                },
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully created SystemVASProduct: {obj.type}"
                    )
                )
                created_count += 1
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"SystemVASProduct already exists: {obj.type} (skipped)"
                    )
                )
                skipped_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"\nSeeding complete. {created_count} products created, "
                f"{skipped_count} products skipped."
            )
        )
