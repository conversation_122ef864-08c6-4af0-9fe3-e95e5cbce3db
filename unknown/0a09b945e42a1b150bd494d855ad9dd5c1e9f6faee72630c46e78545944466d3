import pytest
from business.enums import SocialMediaChannel
from business.models import SocialMedia
from business.tests.fixtures import create_business, create_test_user
from business.v1.serializers import MultipleSocialMediaSerializer, SocialMediaSerializer


@pytest.mark.django_db
class TestSocialMediaSerializer:
    """Test cases for the SocialMediaSerializer"""

    def test_valid_data_serialization(self):
        """Test serializer with valid data"""
        user = create_test_user(email="<EMAIL>")

        valid_data = {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness",
                }
            ]
        }

        serializer = MultipleSocialMediaSerializer(
            data=valid_data, context={"user": user}
        )
        assert serializer.is_valid() is True

    def test_invalid_channel(self):
        """Test serializer with invalid channel"""
        user = create_test_user(email="<EMAIL>")

        invalid_channel_data = {
            "social_medias": [
                {"channel": "MySpace", "url": "https://facebook.com/mybusiness"}
            ]
        }

        serializer = MultipleSocialMediaSerializer(
            data=invalid_channel_data, context={"user": user}
        )
        assert serializer.is_valid() is False

        assert "social_medias" in serializer.errors
        assert isinstance(serializer.errors["social_medias"], list)
        assert "channel" in serializer.errors["social_medias"][0]

    def test_invalid_url(self):
        """Test serializer with invalid URL"""
        user = create_test_user(email="<EMAIL>")

        invalid_url_data = {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "facebook.com/mybusiness",
                }
            ]
        }

        serializer = MultipleSocialMediaSerializer(
            data=invalid_url_data, context={"user": user}
        )
        assert serializer.is_valid() is False
        assert "social_medias" in serializer.errors
        assert isinstance(serializer.errors["social_medias"], list)
        assert "url" in serializer.errors["social_medias"][0]

    def test_create_method(self):
        """Test the create method"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        valid_data = {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness",
                }
            ]
        }

        serializer = MultipleSocialMediaSerializer(
            data=valid_data, context={"user": user}
        )
        assert serializer.is_valid() is True

        serializer.save()

        SocialMedia.objects.filter(
            business=business,
            channel=valid_data["social_medias"][0]["channel"],
            url=valid_data["social_medias"][0]["url"],
        ).exists()

    def test_update_existing_channel(self):
        """Test updating an existing channel"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        # Create a channel first
        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Facebook.value,
            url="https://facebook.com/original",
        )

        update_data = {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/updated",
                }
            ]
        }

        serializer = MultipleSocialMediaSerializer(
            data=update_data, context={"user": user}
        )
        assert serializer.is_valid() is True

        serializer.save()

        social_media = SocialMedia.objects.filter(
            business=business,
            channel=SocialMediaChannel.Facebook.value,
            url="https://facebook.com/updated",
        ).exists()
        assert social_media is True


@pytest.mark.django_db
class TestSocialMediaListSerializer:
    """Test cases for the SocialMediaListSerializer"""

    def test_list_serialization(self):
        """Test serializing a list of social media channels"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        # Create some social media channels
        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Facebook.value,
            url="https://facebook.com/mybusiness",
        )
        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Twitter.value,
            url="https://twitter.com/mybusiness",
        )

        social_media_qs = SocialMedia.objects.filter(business=business)
        serializer = SocialMediaSerializer(social_media_qs, many=True)
        data = serializer.data

        assert isinstance(data, list)
        assert len(data) == 2

    def test_empty_list_serialization(self):
        """Test serializing an empty list"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        social_media = SocialMedia.objects.filter(business=business)
        serializer = MultipleSocialMediaSerializer(social_media, many=True)

        data = serializer.data

        assert len(data) == 0
        assert data == []
