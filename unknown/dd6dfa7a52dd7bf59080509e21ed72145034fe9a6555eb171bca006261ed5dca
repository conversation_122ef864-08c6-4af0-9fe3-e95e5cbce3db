import json
from typing import Any, Dict, <PERSON><PERSON>

import pytest
from business.enums import SocialMediaChannel
from business.models import SocialMedia
from business.tests.fixtures import create_business, create_test_user
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient


@pytest.mark.django_db
class TestSocialMediaEndpoints:
    """Comprehensive test cases for social media endpoints"""

    @pytest.fixture
    def api_client(self) -> APIClient:
        """Return an API client for testing"""
        return APIClient()

    @pytest.fixture
    def authenticated_client(self, api_client: APIClient) -> Tuple[APIClient, Any, Any]:
        """Return an authenticated API client with user and business"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)
        api_client.force_authenticate(user=user)
        return api_client, user, business

    @pytest.fixture
    def social_media_urls(self) -> Dict[str, str]:
        """Return URLs for social media endpoints"""
        return {
            "add": reverse("business:onboard-business-add-social-media"),
            "list": reverse("business:onboard-business-list-social-media"),
        }

    @pytest.fixture
    def valid_social_media_data(self) -> Dict[str, Any]:
        """Return valid social media data for testing"""
        return {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness",
                },
                {
                    "channel": SocialMediaChannel.Twitter.value,
                    "url": "https://twitter.com/mybusiness",
                },
                {
                    "channel": SocialMediaChannel.Instagram.value,
                    "url": "https://instagram.com/mybusiness",
                },
                {
                    "channel": SocialMediaChannel.LinkedIn.value,
                    "url": "https://linkedin.com/company/mybusiness",
                },
            ]
        }

    @pytest.fixture
    def single_social_media_data(self) -> Dict[str, Any]:
        """Return single social media entry for testing"""
        return {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness",
                }
            ]
        }

    @pytest.fixture
    def invalid_social_media_data(self) -> Dict[str, Dict[str, Any]]:
        """Return invalid social media data for testing"""
        return {
            "invalid_channel": {
                "social_medias": [
                    {
                        "channel": "MySpace",
                        "url": "https://myspace.com/mybusiness",
                    }
                ]
            },
            "invalid_url": {
                "social_medias": [
                    {
                        "channel": SocialMediaChannel.Facebook.value,
                        "url": "facebook.com/mybusiness",  # Missing protocol
                    }
                ]
            },
            "empty_channel": {
                "social_medias": [
                    {
                        "channel": "",
                        "url": "https://facebook.com/mybusiness",
                    }
                ]
            },
            "empty_url": {
                "social_medias": [
                    {
                        "channel": SocialMediaChannel.Facebook.value,
                        "url": "",
                    }
                ]
            },
            "missing_social_medias_key": {
                "channel": SocialMediaChannel.Facebook.value,
                "url": "https://facebook.com/mybusiness",
            },
            "empty_social_medias_array": {"social_medias": []},
            "invalid_social_medias_structure": {"social_medias": "not an array"},
            "missing_channel": {
                "social_medias": [{"url": "https://facebook.com/mybusiness"}]
            },
            "missing_url": {
                "social_medias": [{"channel": SocialMediaChannel.Facebook.value}]
            },
            "multiple_invalid_entries": {
                "social_medias": [
                    {
                        "channel": "InvalidChannel",
                        "url": "https://facebook.com/mybusiness",
                    },
                    {"channel": SocialMediaChannel.Twitter.value, "url": "invalid-url"},
                ]
            },
        }

    def _create_social_media(
        self, client: APIClient, social_media_urls: Dict[str, str], data: Dict[str, Any]
    ) -> Any:
        """Helper method to create social media entry"""
        response = client.post(
            social_media_urls["add"],
            data=json.dumps(data),
            content_type="application/json",
        )
        return response

    def _assert_social_media_exists(
        self, business: Any, channel: str, url: str
    ) -> None:
        """Helper method to assert social media exists with correct data"""
        social_media = SocialMedia.objects.filter(
            business=business, channel=channel
        ).first()
        assert social_media is not None
        assert social_media.url == url

    def _assert_social_media_count(
        self, business: Any, channel: str, expected_count: int
    ) -> None:
        """Helper method to assert social media count for a channel"""
        count = SocialMedia.objects.filter(business=business, channel=channel).count()
        assert count == expected_count

    def _get_first_social_media_from_payload(
        self, payload: Dict[str, Any]
    ) -> Dict[str, str]:
        """Helper method to get first social media entry from payload"""
        return payload["social_medias"][0]

    # Test successful operations
    def test_add_single_social_media_success(
        self, authenticated_client, social_media_urls, single_social_media_data
    ):
        """Test successful addition of a single social media channel"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, single_social_media_data
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["message"] == "Social media channel added successfully"

        first_social_media = self._get_first_social_media_from_payload(
            single_social_media_data
        )
        self._assert_social_media_exists(
            business, first_social_media["channel"], first_social_media["url"]
        )

    def test_add_multiple_social_media_success(
        self, authenticated_client, social_media_urls, valid_social_media_data
    ):
        """Test successful addition of multiple social media channels"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, valid_social_media_data
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

        # Verify all channels were created
        for social_media in valid_social_media_data["social_medias"]:
            self._assert_social_media_exists(
                business, social_media["channel"], social_media["url"]
            )

    def test_update_existing_social_media(
        self, authenticated_client, social_media_urls, single_social_media_data
    ):
        """Test updating an existing social media channel"""
        client, user, business = authenticated_client

        # Create initial social media
        self._create_social_media(client, social_media_urls, single_social_media_data)

        # Update with new URL
        updated_data = {
            "social_medias": [
                {
                    "channel": self._get_first_social_media_from_payload(
                        single_social_media_data
                    )["channel"],
                    "url": "https://facebook.com/mybusiness-updated",
                }
            ]
        }

        response = self._create_social_media(client, social_media_urls, updated_data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

        # Verify only one record exists with updated URL
        original_channel = self._get_first_social_media_from_payload(
            single_social_media_data
        )["channel"]
        updated_channel = self._get_first_social_media_from_payload(updated_data)[
            "channel"
        ]
        updated_url = self._get_first_social_media_from_payload(updated_data)["url"]

        self._assert_social_media_count(business, original_channel, 1)
        self._assert_social_media_exists(business, updated_channel, updated_url)

    # Test validation and error cases
    def test_add_social_media_invalid_channel(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with invalid channel"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["invalid_channel"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_malformed_json(
        self, authenticated_client, social_media_urls
    ):
        """Test adding social media with malformed JSON"""
        client, user, business = authenticated_client

        response = client.post(
            social_media_urls["add"],
            data="{invalid json}",
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_invalid_url(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with invalid URL format"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["invalid_url"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_empty_channel(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with empty channel"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["empty_channel"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_empty_url(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with empty URL"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["empty_url"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_missing_social_medias_key(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media without social_medias key"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client,
            social_media_urls,
            invalid_social_media_data["missing_social_medias_key"],
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_empty_social_medias_array(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with empty social_medias array"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client,
            social_media_urls,
            invalid_social_media_data["empty_social_medias_array"],
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_invalid_social_medias_structure(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with invalid social_medias structure"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client,
            social_media_urls,
            invalid_social_media_data["invalid_social_medias_structure"],
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_missing_channel(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with missing channel field"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["missing_channel"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_missing_url(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with missing url field"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, invalid_social_media_data["missing_url"]
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_add_social_media_multiple_invalid_entries(
        self, authenticated_client, social_media_urls, invalid_social_media_data
    ):
        """Test adding social media with multiple invalid entries"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client,
            social_media_urls,
            invalid_social_media_data["multiple_invalid_entries"],
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    # Test authentication
    def test_add_social_media_unauthenticated(
        self, api_client, social_media_urls, single_social_media_data
    ):
        """Test adding social media without authentication"""
        response = api_client.post(
            social_media_urls["add"],
            data=json.dumps(single_social_media_data),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_social_media_unauthenticated(self, api_client, social_media_urls):
        """Test listing social media without authentication"""
        response = api_client.get(social_media_urls["list"])
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    # Test listing functionality
    def test_list_social_media_empty(self, authenticated_client, social_media_urls):
        """Test listing social media channels when none exist"""
        client, user, business = authenticated_client

        response = client.get(social_media_urls["list"])

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert len(response.data["data"]) == 0

    def test_list_social_media_with_channels(
        self, authenticated_client, social_media_urls, valid_social_media_data
    ):
        """Test listing social media channels when some exist"""
        client, user, business = authenticated_client

        # Add multiple social media channels
        self._create_social_media(client, social_media_urls, valid_social_media_data)

        response = client.get(social_media_urls["list"])

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert len(response.data["data"]) == len(
            valid_social_media_data["social_medias"]
        )

        # Verify all channels are in the response
        response_channels = [item["channel"] for item in response.data["data"]]
        expected_channels = [
            social_media["channel"]
            for social_media in valid_social_media_data["social_medias"]
        ]

        for channel in expected_channels:
            assert channel in response_channels

    def test_list_social_media_response_structure(
        self, authenticated_client, social_media_urls, single_social_media_data
    ):
        """Test the structure of list response"""
        client, user, business = authenticated_client

        self._create_social_media(client, social_media_urls, single_social_media_data)

        response = client.get(social_media_urls["list"])

        assert response.status_code == status.HTTP_200_OK
        assert "success" in response.data
        assert "data" in response.data
        assert isinstance(response.data["data"], list)

        if response.data["data"]:
            item = response.data["data"][0]
            assert "id" in item
            assert "channel" in item
            assert "url" in item

    # Test deletion functionality
    def test_delete_social_media_success(
        self, authenticated_client, social_media_urls, single_social_media_data
    ):
        """Test successful deletion of a social media channel"""
        client, user, business = authenticated_client

        response = self._create_social_media(
            client, social_media_urls, single_social_media_data
        )
        social_media_id = SocialMedia.objects.first().id

        delete_url = reverse(
            "business:onboard-business-delete-social-media",
            kwargs={"social_media_id": social_media_id},
        )
        response = client.delete(delete_url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["message"] == "Social media channel deleted successfully"
        assert not SocialMedia.objects.filter(id=social_media_id).exists()

    def test_delete_social_media_not_found(self, authenticated_client):
        """Test deleting a non-existent social media channel"""
        client, user, business = authenticated_client

        delete_url = reverse(
            "business:onboard-business-delete-social-media",
            kwargs={"social_media_id": "non-existent-id"},
        )
        response = client.delete(delete_url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data["success"] is False
        assert response.data["message"] == "Social media channel not found"

    def test_delete_social_media_unauthenticated(self, api_client):
        """Test deleting social media without authentication"""
        delete_url = reverse(
            "business:onboard-business-delete-social-media",
            kwargs={"social_media_id": "some-id"},
        )
        response = api_client.delete(delete_url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_delete_social_media_wrong_business(
        self, authenticated_client, single_social_media_data
    ):
        """Test deleting social media from different business fails"""
        client, user, business = authenticated_client

        # Create another user and business
        other_user = create_test_user(email="<EMAIL>")
        other_business = create_business(
            owner=other_user,
            email="<EMAIL>",
            phone="0812222222",
            rc_number="RC1X23456",
            website="https://test2website.com",
        )

        # Create social media for other business
        other_social_media_data = {
            "social_medias": [
                {
                    "channel": self._get_first_social_media_from_payload(
                        single_social_media_data
                    )["channel"],
                    "url": self._get_first_social_media_from_payload(
                        single_social_media_data
                    )["url"],
                }
            ]
        }

        social_media = SocialMedia.objects.create(
            business=other_business,
            channel=self._get_first_social_media_from_payload(other_social_media_data)[
                "channel"
            ],
            url=self._get_first_social_media_from_payload(other_social_media_data)[
                "url"
            ],
        )

        delete_url = reverse(
            "business:onboard-business-delete-social-media",
            kwargs={"social_media_id": social_media.id},
        )
        response = client.delete(delete_url)

        # Should not be able to delete other business's social media
        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_403_FORBIDDEN,
        ]

    # Test edge cases
    def test_add_duplicate_channels_in_single_request(
        self, authenticated_client, social_media_urls
    ):
        """Test adding duplicate channels in a single request"""
        client, user, business = authenticated_client

        duplicate_data = {
            "social_medias": [
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness1",
                },
                {
                    "channel": SocialMediaChannel.Facebook.value,
                    "url": "https://facebook.com/mybusiness2",
                },
            ]
        }

        response = self._create_social_media(client, social_media_urls, duplicate_data)

        # Behavior depends on implementation - could be success with last one winning
        # or validation error. Test what actually happens.
        if response.status_code == status.HTTP_200_OK:
            # If successful, verify only one Facebook entry exists
            self._assert_social_media_count(
                business, SocialMediaChannel.Facebook.value, 1
            )
        else:
            # If validation error, should be 400
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_case_sensitivity_in_channels(
        self, authenticated_client, social_media_urls
    ):
        """Test case sensitivity in channel names"""
        client, user, business = authenticated_client

        case_test_data = {
            "social_medias": [
                {
                    "channel": "facebook",  # lowercase
                    "url": "https://facebook.com/mybusiness",
                }
            ]
        }

        response = self._create_social_media(client, social_media_urls, case_test_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
