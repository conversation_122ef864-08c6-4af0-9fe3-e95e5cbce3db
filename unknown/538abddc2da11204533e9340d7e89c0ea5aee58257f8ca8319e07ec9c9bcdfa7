<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Dispute Assignment Notification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .dispute-details { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .status-badge { padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }
        .pending { background-color: #ffc107; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🎫 Dispute Assignment Notification</h2>
            <p>Hello <strong>{{ assignee_name }}</strong>,</p>
            <p>You have been assigned a new dispute ticket by <strong>{{ assigner_name }}</strong>.</p>
        </div>

        <div class="dispute-details">
            <h3>📋 Dispute Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Ticket ID:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">#{{ dispute_id }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Transaction Reference:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ transaction_reference }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Business:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ business_name }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Merchant:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ merchant_name }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Service Type:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ vas_service }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Amount:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">₦{{ amount }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Status:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
                        <span class="status-badge pending">PENDING</span>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Created:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ created_at|date:"F d, Y at g:i A" }}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Assigned:</strong></td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ assignment_date|date:"F d, Y at g:i A" }}</td>
                </tr>
            </table>
        </div>

        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #ffc107;">
            <h4>💬 Merchant's Query:</h4>
            <p style="font-style: italic;">"{{ dispute_message }}"</p>
        </div>

        {% if assignment_notes %}
        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #17a2b8;">
            <h4>📝 Assignment Notes:</h4>
            <p>{{ assignment_notes }}</p>
        </div>
        {% endif %}

        <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4>🚀 Next Steps:</h4>
            <ul>
                <li>Review the dispute details carefully</li>
                <li>Investigate the transaction and merchant's concern</li>
                <li>Respond to the dispute with your findings</li>
                <li>Update the status as appropriate (In Review → Resolved)</li>
            </ul>
        </div>

        <p>Please log into the admin panel to review and respond to this dispute.</p>

        <div class="footer">
            <p>This is an automated notification from the SageCloud Dispute Management System.</p>
            <p>© {{ "now"|date:"Y" }} SageCloud. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
