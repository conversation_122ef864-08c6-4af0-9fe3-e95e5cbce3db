import json
from unittest.mock import MagicMock, patch

from business.enums import Document<PERSON>ame, DocumentStatus, OnboardingStage
from business.models import Document
from business.tests.fixtures import (
    create_business,
    create_test_user,
    get_valid_business_data,
)
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from pykolofinance.common.helpers import clean_phone_number
from rest_framework import status
from rest_framework.test import APIClient, APITestCase


class OnboardBusinessViewSetTest(APITestCase):
    """Test cases for OnboardBusinessViewSet"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        # Create test user
        self.user = create_test_user()

        # Create business for the user
        self.business = create_business(owner=self.user)

        # Authenticate user
        self.client.force_authenticate(user=self.user)

        # URL for the collect-information endpoint
        self.submit_business_information_url = reverse(
            "business:onboard-business-submit-business-information"
        )
        self.upload_url = reverse("business:onboard-business-upload-documents")
        self.add_social_media_url = reverse(
            "business:onboard-business-add-social-media"
        )
        self.next_onboarding_stage_url = reverse(
            "business:onboard-business-next-onboarding-stage"
        )

        # Get valid business data from fixture
        self.valid_payload = get_valid_business_data()

    def test_submit_business_information_success(self):
        """Test successful submission of business information"""
        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["success"], True)
        self.assertEqual(
            response.data["message"], "Business information submitted successfully"
        )

        # Verify business was updated in database
        self.business.refresh_from_db()
        self.assertEqual(self.business.email, self.valid_payload["email"])
        self.assertEqual(self.business.description, self.valid_payload["description"])
        self.assertEqual(
            self.business.phone, clean_phone_number(self.valid_payload["phone"])
        )
        self.assertEqual(
            self.business.onboarding_stage, OnboardingStage.Documentation.value
        )

    def test_submit_business_information_unauthenticated(self):
        """Test submission without authentication"""
        # Log out the client
        self.client.force_authenticate(user=None)

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_submit_business_information_invalid_data(self):
        """Test submission with invalid data"""
        invalid_payload = {**self.valid_payload, "email": "invalid-email"}

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(invalid_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("email", response.data)

    def test_submit_business_information_duplicate_email(self):
        """Test submission with duplicate email"""
        # Create another business with an email to cause a conflict
        other_user = create_test_user(email="<EMAIL>")

        create_business(
            name="Other Business",
            owner=other_user,
            email="<EMAIL>",
            phone="9876543210",
            rc_number="RC789012",
            website="https://sage-test.com",
        )

        duplicate_payload = {
            **self.valid_payload,
            "email": "<EMAIL>",
            "website": "https://sage.com",
        }

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(duplicate_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("email", response.data)
        self.assertEqual(
            response.data["email"][0], "business with this email already exists."
        )

    def test_submit_business_information_duplicate_phone(self):
        """Test submission with duplicate phone"""
        # Create another business with a phone to cause a conflict
        user = create_test_user(email="<EMAIL>")

        create_business(
            name="Phone Business",
            owner=user,
            email="<EMAIL>",
            phone="***********",
            rc_number="RC555555",
            website="https://sage-test.com",
        )

        payload = {
            **self.valid_payload,
            "phone": "***********",
            "website": "https://sage.com",
        }

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("phone", response.data)
        self.assertEqual(
            response.data["phone"][0], "business with this phone already exists."
        )

    @patch("business.v1.serializers.clean_phone_number")
    def test_phone_number_cleaning_in_view(self, mock_clean_phone):
        """Test that clean_phone_number is called when submitting through the view"""
        mock_clean_phone.return_value = "+2341234567890"

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_clean_phone.assert_called_once_with(self.valid_payload["phone"].strip())

    @patch("business.v1.serializers.validate_email")
    def test_email_validation_in_view(self, mock_validate_email):
        """Test that validate_email is called when submitting through the view"""
        mock_email = MagicMock()
        mock_email.normalized = "<EMAIL>"
        mock_validate_email.return_value = mock_email

        response = self.client.post(
            self.submit_business_information_url,
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_validate_email.assert_called_once_with(self.valid_payload["email"])

        # Verify normalized email was used
        self.business.refresh_from_db()
        self.assertEqual(self.business.email, "<EMAIL>")

    def test_upload_document_success(self):
        temp_file = SimpleUploadedFile(
            name="test_document.pdf",
            content=b"file_content",
            content_type="application/pdf",
        )

        data = {
            "document_name": DocumentName.MemorandumOfAssociation.value,
            "document": temp_file,
        }

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["success"], True)
        self.assertEqual(response.data["message"], "Document uploaded successfully")

        # Verify document was created in database
        document = Document.objects.filter(
            business=self.business,
            document_name=DocumentName.MemorandumOfAssociation.value,
        ).first()

        self.assertIsNotNone(document)
        self.assertEqual(document.status, DocumentStatus.Pending.value)

    def test_upload_document_update_existing(self):
        """Test updating an existing document"""
        # Create an existing document
        existing_doc = Document.objects.create(
            business=self.business,
            document_name=DocumentName.CertificateOfIncorporation.value,
            document=SimpleUploadedFile(
                name="old_document.pdf",
                content=b"old_content",
                content_type="application/pdf",
            ),
            status=DocumentStatus.Rejected.value,
            rejection_message="Document is unclear",
        )

        # Create a new document to upload
        new_file = SimpleUploadedFile(
            name="new_document.pdf",
            content=b"new_content",
            content_type="application/pdf",
        )

        data = {
            "document_name": DocumentName.CertificateOfIncorporation.value,
            "document": new_file,
        }

        response = self.client.post(self.upload_url, data, format="multipart")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify document was updated in database
        document = Document.objects.get(
            business=self.business,
            document_name=DocumentName.CertificateOfIncorporation.value,
        )

        self.assertEqual(document.status, DocumentStatus.Pending.value)
        self.assertNotEqual(document.document.name, existing_doc.document.name)

    def test_upload_document_invalid_document_name(self):
        """Test upload with invalid document name"""
        # Create a temporary file
        temp_file = SimpleUploadedFile(
            name="test_document.pdf",
            content=b"file_content",
            content_type="application/pdf",
        )

        # Prepare data with invalid document_name
        data = {"document_name": "InvalidDocumentName", "document": temp_file}

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("document_name", response.data)

    def test_upload_document_missing_file(self):
        """Test upload with missing file"""
        # Prepare data without document file
        data = {
            "document_name": DocumentName.MemorandumOfAssociation.value,
        }

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("document", response.data)

    def test_upload_document_unauthenticated(self):
        """Test upload without authentication"""
        # Logout the user
        self.client.force_authenticate(user=None)

        # Create a temporary file
        temp_file = SimpleUploadedFile(
            name="test_document.pdf",
            content=b"file_content",
            content_type="application/pdf",
        )

        # Prepare data for request
        data = {
            "document_name": DocumentName.MemorandumOfAssociation.value,
            "document": temp_file,
        }

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_upload_document_large_file(self):
        """Test uploading a large file"""
        # Create a large temporary file (10MB)
        large_content = b"x" * (10 * 1024 * 1024)  # 10MB of data
        large_file = SimpleUploadedFile(
            name="large_document.pdf",
            content=large_content,
            content_type="application/pdf",
        )

        # Prepare data for request
        data = {
            "document_name": DocumentName.MemorandumOfAssociation.value,
            "document": large_file,
        }

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # Check response (this assumes your app allows 10MB files)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_upload_document_unsupported_file_type(self):
        """Test uploading an unsupported file type (if your app validates this)"""
        # This test assumes your app might validate file types
        # If it doesn't, this test might pass even though it shouldn't

        # Create a file with unsupported type
        invalid_file = SimpleUploadedFile(
            name="test_document.exe",
            content=b"file_content",
            content_type="application/octet-stream",
        )

        # Prepare data for request
        data = {
            "document_name": DocumentName.MemorandumOfAssociation.value,
            "document": invalid_file,
        }

        # Make the request
        response = self.client.post(self.upload_url, data, format="multipart")

        # This will pass if your app accepts any file type, but should fail if
        # there's validation on file types
        if response.status_code == status.HTTP_400_BAD_REQUEST:
            self.assertIn("document", response.data)

    def test_upload_all_required_documents_updates_onboarding_stage(self):
        """Test that uploading all required documents updates the onboarding stage"""
        required_doc_values = [
            doc[0] for doc in DocumentName.for_upload_documents_screen()
        ]

        self.business.onboarding_stage = OnboardingStage.Documentation.value
        self.business.save()

        response = self.client.post(
            self.add_social_media_url,
            {"social_medias": [{"channel": "Facebook", "url": "https://facebook.com"}]},
            content_type="application/json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        for doc_name in required_doc_values:
            temp_file = SimpleUploadedFile(
                name=f"{doc_name}.pdf",
                content=f"{doc_name}_content".encode(),
                content_type="application/pdf",
            )

            data = {"document_name": doc_name, "document": temp_file}

            response = self.client.post(self.upload_url, data, format="multipart")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # Verify all documents have been stored
        for doc_name in required_doc_values:
            document = Document.objects.filter(
                business=self.business, document_name=doc_name
            ).first()
            self.assertIsNotNone(document)

        # Verify onboarding stage has been updated
        self.business.refresh_from_db()
        self.assertEqual(
            self.business.onboarding_stage, OnboardingStage.DirectorsAndOwners.value
        )

    def test_upload_all_required_documents_except_one_does_not_updates_on_boarding_stage(
        self,
    ):
        required_doc_values = [
            DocumentName.CertificateOfIncorporation.value,
            DocumentName.ProofOfAddress.value,
            DocumentName.MemorandumOfAssociation.value,
        ]

        self.business.onboarding_stage = OnboardingStage.Documentation.value
        self.business.save()

        # Upload all required documents except the last one
        for doc_name in required_doc_values[:-1]:
            temp_file = SimpleUploadedFile(
                name=f"{doc_name}.pdf",
                content=f"{doc_name}_content".encode(),
                content_type="application/pdf",
            )

            data = {"document_name": doc_name, "document": temp_file}

            response = self.client.post(self.upload_url, data, format="multipart")
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify onboarding stage hasn't changed yet
        self.business.refresh_from_db()
        self.assertEqual(
            self.business.onboarding_stage, OnboardingStage.Documentation.value
        )

    def test_next_onboarding_stage(self):
        """Test retrieving the next onboarding stage"""
        # Set the onboarding stage
        self.business.onboarding_stage = OnboardingStage.Documentation.value
        self.business.save()

        # Make the request
        response = self.client.get(self.next_onboarding_stage_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["success"], True)
        self.assertEqual(
            response.data["next_onboarding_stage"], OnboardingStage.Documentation.value
        )

    def test_next_onboarding_stage_unauthenticated(self):
        """Test retrieving the next onboarding stage without authentication"""
        # Logout the user
        self.client.force_authenticate(user=None)

        # Make the request
        response = self.client.get(self.next_onboarding_stage_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
