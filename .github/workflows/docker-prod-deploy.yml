name: Production Docker Deployment

on:
  push:
    branches: [main]

jobs:
  docker-build-push:
    runs-on: ubuntu-latest

    steps:
    # Step 1: Check out the repository
    - name: Checkout repository
      uses: actions/checkout@v4

    # Step 2: Set up Docker Buildx for multi-platform builds (optional)
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    # Step 3: Log in to Docker Hub
    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    # Step 4: Build and tag the Docker image
    - name: Build Docker image app
      run: |
        docker build -f docker/prod/Dockerfile -t capitalsage/phlox-wallet:production .

    - name: Build Docker image for Celery
      run: |
        docker build -f docker/celery/Dockerfile-celery -t capitalsage/phlox-wallet-celery:production .

    # Step 5: Push the Docker image to Docker Hub
    - name: Push Docker image
      run: |
        docker push capitalsage/phlox-wallet:production
        docker push capitalsage/phlox-wallet-celery:production

    # Optional: Tag the Docker image with the current commit SHA
    - name: Tag Docker image with commit SHA
      run: |
        IMAGE_TAG=${{ github.sha }}
        docker tag capitalsage/phlox-wallet:production capitalsage/phlox-wallet:${IMAGE_TAG}
        docker tag capitalsage/phlox-wallet-celery:production capitalsage/phlox-wallet-celery:${IMAGE_TAG}
        docker push capitalsage/phlox-wallet:${IMAGE_TAG}
        docker push capitalsage/phlox-wallet-celery:${IMAGE_TAG}
