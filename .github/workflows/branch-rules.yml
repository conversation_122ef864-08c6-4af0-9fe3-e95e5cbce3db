name: BranchNamingPolicy

on:
  create:
  delete:
  pull_request:
    branches:
      - api

jobs:
  branch-naming-policy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Run Branch Naming Policy Action
        uses: nicklegan/github-repo-branch-naming-policy-action@v1.1.1
        if: github.ref_type == 'branch' || github.ref_type == 'pull_request'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          regex: '^([a-z0-9]+)/([a-z0-9]+)/([a-z0-9]+)-([a-z0-9]+)$'
          flags: i
          # token: ${{ secrets.REPO_TOKEN }}
          # delete: true
