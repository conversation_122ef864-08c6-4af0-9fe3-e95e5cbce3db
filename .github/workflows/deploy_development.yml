name: Deploy Development

on:
  #workflow_run:
   # workflows: [ BranchNamingPolicy ]
    #types:
    #  - completed
  push:
    branches: dev

jobs:
  build:
    runs-on: ubuntu-latest
    # if: ${{ github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success' }}

    steps:
      - uses: actions/checkout@v2

      - name: Push to server and deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_HOST }}
          USERNAME: ${{ secrets.DEV_USERNAME }}
          PORT: ${{ secrets.PORT }}
          KEY: ${{ secrets.DEV_SSHKEY }}
          script: cd ${{ secrets.DEV_PATH }} && ls && git pull && docker compose up --build -d
