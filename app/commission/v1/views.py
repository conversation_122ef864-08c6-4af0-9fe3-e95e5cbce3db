from commission.v1.serializers import CommissionTransactionSerializer
from common.pagination import LargeDatasetKeySetPagination
from rest_framework import status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from transaction.models.base import CommissionTransaction


class CommissionTransactionViewSet(viewsets.ModelViewSet):
    queryset = CommissionTransaction.objects.all()
    serializer_class = CommissionTransactionSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetKeySetPagination

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business=self.request.user.business)
        return queryset

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)
