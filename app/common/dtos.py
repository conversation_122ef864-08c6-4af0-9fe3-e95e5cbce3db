from dataclasses import dataclass
from typing import Optional

from common.utils import ToDict


@dataclass
class CoreServiceResponse(ToDict):
    success: bool
    status: Optional[str] = "pending"
    reference: Optional[str] = None
    message: Optional[str] = None
    data: Optional[dict or list] = None
    errors: Optional[dict or list] = None


@dataclass
class VasGateServiceResponse(ToDict):
    status: Optional[str] = None
    message: Optional[str] = None
    data: Optional[dict or list] = None
    status_code: Optional[int] = None
