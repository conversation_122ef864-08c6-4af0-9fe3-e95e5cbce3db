import logging

from django.conf import settings
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied

logger = logging.getLogger(__name__)


class IsSuperAdmin(permissions.BasePermission):
    """Allows access only to admin users."""

    message = "Only Super Admins are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and request.user.role == "Super Admin"
        )


class IsBusinessOwner(permissions.BasePermission):
    """Allows access only to business owner users."""

    message = "Only Business Owners are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and request.user.role == "Business_Owner"
        )


class IsAdmin(permissions.BasePermission):
    """Allows access only to admin users."""

    message = "Only Admins are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and request.user.role == "Admin"
        )


class IsAdminViewOnly(permissions.BasePermission):
    """Allows access only to admin users."""

    message = "Only Admins are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and request.user.role == "Admin View Only"
        )


class IsPlatformOwner(permissions.BasePermission):
    """
    Allows access only to users who are either Admin (console) or Business_Owner (merchant).
    """

    def has_permission(self, request, view):
        allowed_roles = ["Admin", "Business_Owner"]
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.role not in allowed_roles:
            raise PermissionDenied("You do not have permission to manage roles.")

        return True


class IsSafeIPAddress(permissions.BasePermission):
    """
    Ensure the request's IP address is on the safe list configured in Django settings.
    """

    @staticmethod
    def get_client_ip(request):
        ip_addresses = [
            request.META.get("REMOTE_ADDR", ""),
            request.META.get("HTTP_X_FORWARDED_FOR", ""),
        ]
        return [addr for addr in ip_addresses if addr]

    def has_permission(self, request, view):
        remote_addresses = self.get_client_ip(request)
        logger.info({"remote_addresses": remote_addresses})
        if settings.DEBUG:
            return True
        return any(
            element in remote_addresses for element in settings.PHLOX_SAFE_LIST_IPS
        )


class IsSafeInwardIPAddress(permissions.BasePermission):
    """
    Ensure the request's IP address is on the safe list configured in Django settings.
    """

    @staticmethod
    def get_client_ip(request):
        ip_addresses = [
            request.META.get("REMOTE_ADDR", ""),
            request.META.get("HTTP_X_FORWARDED_FOR", ""),
        ]
        return [addr for addr in ip_addresses if addr]

    def has_permission(self, request, view):
        remote_addresses = self.get_client_ip(request)
        logger.info({"remote_addresses": remote_addresses})
        if settings.DEBUG:
            return True
        return any(
            element in remote_addresses
            for element in settings.PHLOX_INWARD_SAFE_LIST_IPS
        )
