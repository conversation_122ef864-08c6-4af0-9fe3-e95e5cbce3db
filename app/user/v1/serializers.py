import logging

import pyotp
from django.conf import settings
from django.contrib.auth import authenticate
from django.utils.crypto import get_random_string
from django.utils.translation import gettext_lazy as _
from email_validator import EmailNotValidError, validate_email
from pykolofinance.common.helpers import clean_phone_number
from rest_framework import exceptions, serializers
from rest_framework.validators import UniqueValidator
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from user.enums import Auth2FATypeEnums
from user.models import Token, User
from user.tasks import send_registration_email
from user.utils import cache_user_session_key, generate_token, get_user_session_key

logger = logging.getLogger(__name__)


class UserMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "firstname", "lastname", "email"]


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "firstname",
            "lastname",
            "email",
            "role",
            "image",
            "verified",
            "last_login",
            "created_at",
        ]


class CreateUserSerializer(serializers.ModelSerializer):
    """Serializer for user object"""

    class Meta:
        model = User
        fields = ("id", "firstname", "lastname", "email", "phone", "role")

    def validate(self, attrs):
        email = attrs.get("email", None)
        if email:
            email = attrs["email"].lower().strip()
            if User.objects.filter(email=email).exists():
                raise serializers.ValidationError("Email already exists")
            try:
                validated_email = validate_email(attrs["email"])
                attrs["email"] = validated_email.normalized
                return super().validate(attrs)
            except EmailNotValidError as e:
                raise serializers.ValidationError(e)
        return super().validate(attrs)

    def create(self, validated_data):
        user = User.objects.create_user(
            **validated_data, password=get_random_string(10)
        )
        token_str = generate_token(user)
        token, _ = Token.objects.update_or_create(
            user=user,
            token_type="CreateToken",
            defaults={"user": user, "token_type": "CreateToken", "token": token_str},
        )

        user_data = {
            "id": user.id,
            "email": user.email,
            "fullname": "Team Member",
            "url": f"{settings.CLIENT_URL}/verify-user/?token={token.token}",
        }
        send_registration_email.delay(user_data)
        return user

    def update(self, instance, validated_data):
        # user = self.context['request'].user
        return super().update(instance, validated_data)


class CustomObtainTokenPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        if not user.verified:
            raise exceptions.AuthenticationFailed(
                _("Account not yet verified."), code="authentication"
            )
        token = super().get_token(user)
        # Add custom claims
        token.id = user.id
        token["email"] = user.email
        token["role"] = user.role
        token["fullname"] = user.fullname
        token["phone"] = user.phone
        user.save_last_login()
        return token


class AuthTokenSerializer(serializers.Serializer):
    """Serializer for user authentication object"""

    email = serializers.CharField()
    password = serializers.CharField(
        style={"input_type": "password"}, trim_whitespace=False
    )

    def validate(self, attrs):
        """Validate and authenticate the user"""
        email = attrs.get("email")
        password = attrs.get("password")

        if email:
            user = authenticate(
                request=self.context.get("request"),
                username=email.lower().strip(),
                password=password,
            )

            if not user:
                msg = _("Unable to authenticate with provided credentials")
                raise serializers.ValidationError(msg, code="authentication")
            attrs["user"] = user
        return attrs


class VerifyTokenSerializer(serializers.Serializer):
    """Serializer for token verification"""

    token = serializers.CharField(required=True)


class InitPasswordResetSerializer(serializers.Serializer):
    """Serializer for sending password reset email to the user"""

    email = serializers.CharField(required=True)


class CreatePasswordSerializer(serializers.Serializer):
    """Serializer for creating password for a new user"""

    token = serializers.CharField(required=True, write_only=True)
    password = serializers.CharField(required=True)


class PinSerializer(serializers.Serializer):
    pin = serializers.CharField(required=True)


class TokenDecodeSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)


class LoginRequestSerializer(serializers.Serializer):
    """Serializer for requesting login OTP"""

    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)


class OTPVerificationSerializer(serializers.Serializer):
    """Serializer for OTP verification"""

    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, min_length=6, max_length=6)


class BusinessUserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for business user registration"""

    business_name = serializers.CharField(required=True)
    email = serializers.EmailField(
        validators=[
            UniqueValidator(queryset=User.objects.all(), message="Email already exists")
        ]
    )

    class Meta:
        model = User
        fields = ("business_name", "firstname", "lastname", "email", "phone")

    def validate(self, attrs):
        logger.info("Validating business user registration data")
        data = super().validate(attrs)
        logger.info(f"Validated data: {data}")
        email = data["email"].lower().strip()
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError({"email": "Email already exists"})

        data["phone"] = clean_phone_number(data["phone"].strip())

        if User.objects.filter(phone=data["phone"]).exists():
            raise serializers.ValidationError({"phone": "Phone already exists"})

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data


class PasswordCreationSerializer(serializers.Serializer):
    """Serializer for creating a password with validation"""

    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, min_length=6, max_length=6)
    password = serializers.CharField(required=True, write_only=True)

    def validate_password(self, value):
        """Validate password complexity"""
        if not any(char.isupper() for char in value):
            raise serializers.ValidationError(
                "Password must contain at least one uppercase letter"
            )
        if not any(char.islower() for char in value):
            raise serializers.ValidationError(
                "Password must contain at least one lowercase letter"
            )
        if not any(char.isdigit() for char in value):
            raise serializers.ValidationError(
                "Password must contain at least one number"
            )
        if not any(char in "!@#$%^&*()_+-=[]{}|;:,.<>?/~`" for char in value):
            raise serializers.ValidationError(
                "Password must contain at least one special character"
            )
        return value


class Auth2FASetupSerializer(serializers.Serializer):
    """
    serializer is meant to setup the user 2FA authentication type and trigger otp
    """

    qr_instance = None
    two_factor_auth_type = serializers.ChoiceField(
        choices=Auth2FATypeEnums.CHOICES, default=Auth2FATypeEnums.TOTP
    )
    qr_code = serializers.CharField(read_only=True)
    email = serializers.EmailField(required=True)
    session_id = serializers.CharField(required=True)

    def create(self, validated_data):
        user = User.objects.filter(email=validated_data.get("email")).first()
        if not user:
            raise serializers.ValidationError({"email": "Invalid record"})
        user_session = get_user_session_key(user.id)
        if not user_session:
            raise serializers.ValidationError({"session": "Invalid Session"})
        if user_session != validated_data.get("session_id"):
            raise serializers.ValidationError({"email": "Invalid Session"})

        user.refresh_from_db()
        qr_code = user.generate_qrcode()
        if not qr_code:
            raise serializers.ValidationError({"email": "2FA already setup"})
        self.qr_instance = qr_code
        return validated_data

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["qr_code"] = self.qr_instance
        return representation


class Finalize2FASetupSerializer(serializers.Serializer):
    """
    serializer is meant to receive otp from the user and validate it
    """

    otp = serializers.CharField(required=True, max_length=6, allow_null=False)
    session_id = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
    user = None

    def validate(self, attrs: dict[str, str]):
        """
        validate the otp received from the user
        """
        user = User.objects.filter(email=attrs.get("email")).first()
        if not user:
            raise serializers.ValidationError({"email": "Invalid record"})
        user_session = get_user_session_key(user.id)
        if not user_session:
            raise serializers.ValidationError({"session": "Invalid Session"})
        if user_session != attrs.get("session_id"):
            raise serializers.ValidationError({"email": "Invalid Session"})
        otp = attrs.get("otp")

        self.user = user
        match user.two_factor_auth_type:
            case Auth2FATypeEnums.TOTP:
                totp = pyotp.TOTP(user.plain_two_factor_auth_secret).now()
                if str(totp) != str(otp):
                    logger.info(f"TOTP {str(totp)} != OTP {str(otp)}")
                    raise serializers.ValidationError(
                        {
                            "otp": "OTP is invalid or expired. Check your Authenticator app"
                        }
                    )
            case _:
                raise serializers.ValidationError(
                    {"user": "Invalid authentication type"}
                )
        if not user.two_factor_auth_enabled:
            user.two_factor_auth_enabled = True
            user.save(update_fields=["two_factor_auth_enabled"])

        return super().validate(attrs)

    @staticmethod
    def return_access_token(user) -> dict[str, str]:
        """
        method to return access token for the user.
        """
        token = CustomObtainTokenPairSerializer().get_token(user)
        token["session_id"] = cache_user_session_key(user.id)
        token["two_factor_auth_type"] = user.two_factor_auth_type
        access_token = token.access_token if hasattr(token, "access_token") else ""
        return {"access": str(access_token), "refresh": str(token)}

    def create(self, validated_data):
        return self.return_access_token(self.user)


class Authenticate2FALoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    session_id = serializers.CharField(read_only=True)
    two_factor_auth_type = serializers.CharField(read_only=True)
    two_factor_auth_enabled = serializers.BooleanField(read_only=True)

    def validate(self, data):
        email = data.get("email").strip()
        password = data.get("password")

        if email and password:
            user: User = authenticate(
                request=self.context.get("request"), username=email, password=password
            )
            if not user:
                raise serializers.ValidationError(
                    {"email": "Invalid login credentials"}
                )
            data["user"] = user
        return data

    def to_representation(self, instance):
        user: User = instance.get("user")
        return {
            "session_id": cache_user_session_key(user.id, 600),
            "two_factor_auth_type": user.two_factor_auth_type,
            "two_factor_auth_enabled": user.two_factor_auth_enabled,
        }


class ResponseSerializer(serializers.Serializer):
    """ """

    access = serializers.CharField(required=True)
    refresh = serializers.CharField(required=True)


class GoogleAuthSerializer(serializers.Serializer):
    id_token = serializers.CharField()
