from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from user.v1.views import (
    Authenticate2FALoginView,
    CustomObtainTokenPairView,
    DecodeJwtTokenView,
    Finalize2FASetupView,
    GoogleAuthView,
    Setup2FAView,
)

app_name = "auth"

urlpatterns = [
    path("login/", CustomObtainTokenPairView.as_view(), name="login"),
    path("google/", GoogleAuthView.as_view(), name="google-auth"),
    path("2fa/setup/", Setup2FAView.as_view(), name="2fa-setup"),
    path("2fa/finalize/", Finalize2FASetupView.as_view(), name="2fa-finalize"),
    path("2fa/login/", Authenticate2FALoginView.as_view(), name="2fa-login"),
    path("token/refresh/", TokenRefreshView.as_view(), name="refresh-token"),
    path("token/verify/", TokenVerifyView.as_view(), name="verify-token"),
    path("token/decode/", DecodeJwtTokenView.as_view(), name="decode-jwt-token"),
    # path('token/', CreateTokenView.as_view(), name='tokens'),
]
