# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.kgs
import django.db.models.deletion
import user.enums
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "id",
                    models.Char<PERSON>ield(
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        db_index=True,
                        max_length=254,
                        unique=True,
                        verbose_name="email address",
                    ),
                ),
                ("password", models.Char<PERSON><PERSON>(max_length=600, null=True)),
                ("transaction_pin", models.Char<PERSON>ield(max_length=400, null=True)),
                ("firstname", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("lastname", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("phone", models.CharField(blank=True, max_length=17, null=True)),
                ("image", models.FileField(blank=True, null=True, upload_to="users/")),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("Admin", "Admin"),
                            ("Initiator", "Initiator"),
                            ("Verifier", "Verifier"),
                            ("Approver", "Approver"),
                            ("Business_Owner", "Business Owner"),
                            ("Merchant_Admin", "Merchant Admin"),
                            ("Customer_Support", "Customer Support"),
                            ("Operations", "Operations"),
                            ("Reconciliation", "Reconciliation"),
                            ("Developer", "Developer"),
                        ],
                        max_length=100,
                    ),
                ),
                ("is_staff", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("last_login", models.DateTimeField(null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("verified", models.BooleanField(default=False)),
                ("otp", models.CharField(blank=True, max_length=6, null=True)),
                ("otp_created_at", models.DateTimeField(blank=True, null=True)),
                ("two_factor_auth_secret", models.TextField(blank=True, null=True)),
                ("two_factor_auth_enabled", models.BooleanField(default=False)),
                (
                    "two_factor_auth_type",
                    models.CharField(
                        choices=[("TOTP", "TOTP"), ("EMAIL", "EMAIL")],
                        default="TOTP",
                        max_length=100,
                    ),
                ),
                ("is_password_set", models.BooleanField(default=False)),
                (
                    "password_last_changed_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "google_sub",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Google user ID (sub).",
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                (
                    "is_google_account_linked",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this user has linked their Google account.",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "ordering": ("lastname", "firstname"),
            },
            bases=(models.Model, user.enums.Auth2FATypeEnums),
        ),
        migrations.CreateModel(
            name="Token",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, null=True)),
                (
                    "token_type",
                    models.CharField(
                        choices=[
                            ("CreateToken", "CreateToken"),
                            ("ResetToken", "ResetToken"),
                        ],
                        default="CreateToken",
                        max_length=100,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
