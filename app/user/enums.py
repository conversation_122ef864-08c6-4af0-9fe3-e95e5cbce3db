from enum import Enum

ROLE_OPTIONS = (
    ("Admin", "Admin"),
    ("Initiator", "Initiator"),
    ("Verifier", "Verifier"),
    ("Approver", "Approver"),
    ("Business_Owner", "Business Owner"),
    ("Merchant_Admin", "Merchant Admin"),
    ("Customer_Support", "Customer Support"),
    ("Operations", "Operations"),
    ("Reconciliation", "Reconciliation"),
    ("Developer", "Developer"),
)

GENDER_OPTION = (
    ("Male", "Male"),
    ("Female", "Female"),
)

TOKEN_TYPE = (
    ("CreateToken", "CreateToken"),
    ("ResetToken", "ResetToken"),
)


class PinEnum(Enum):
    Transaction = "Transaction"
    Transfer = "Transfer"


class Auth2FATypeEnums:
    TOTP = "TOTP"
    EMAIL = "EMAIL"
    CHOICES = [
        (TOTP, TOTP),
        (EMAIL, EMAIL),
    ]
