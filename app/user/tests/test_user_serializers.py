import pytest
from user.v1.serializers import (
    BusinessUserRegistrationSerializer,
    CreatePasswordSerializer,
    LoginRequestSerializer,
    OTPVerificationSerializer,
)


@pytest.mark.django_db
class TestLoginRequestSerializer:
    """Tests for the LoginRequestSerializer"""

    def test_valid_data(self):
        """Test serializer with valid data"""
        data = {"email": "<EMAIL>", "password": "password123"}

        serializer = LoginRequestSerializer(data=data)

        assert serializer.is_valid() is True
        assert serializer.validated_data["email"] == "<EMAIL>"
        assert serializer.validated_data["password"] == "password123"

    def test_missing_email(self):
        """Test serializer with missing email"""
        data = {"password": "password123"}

        serializer = LoginRequestSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors

    def test_missing_password(self):
        """Test serializer with missing password"""
        data = {"email": "<EMAIL>"}

        serializer = LoginRequestSerializer(data=data)

        assert serializer.is_valid() is False
        assert "password" in serializer.errors

    def test_invalid_email_format(self):
        """Test serializer with invalid email format"""
        data = {"email": "invalid-email", "password": "password123"}

        serializer = LoginRequestSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors


@pytest.mark.django_db
class TestOTPVerificationSerializer:
    """Tests for the OTPVerificationSerializer"""

    def test_valid_data(self):
        """Test serializer with valid data"""
        data = {"email": "<EMAIL>", "otp": "123456"}

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is True
        assert serializer.validated_data["email"] == "<EMAIL>"
        assert serializer.validated_data["otp"] == "123456"

    def test_missing_email(self):
        """Test serializer with missing email"""
        data = {"otp": "123456"}

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors

    def test_missing_otp(self):
        """Test serializer with missing OTP"""
        data = {"email": "<EMAIL>"}

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "otp" in serializer.errors

    def test_invalid_email_format(self):
        """Test serializer with invalid email format"""
        data = {"email": "invalid-email", "otp": "123456"}

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors

    def test_otp_too_short(self):
        """Test serializer with OTP that's too short"""
        data = {"email": "<EMAIL>", "otp": "12345"}  # 5 digits instead of 6

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "otp" in serializer.errors

    def test_otp_too_long(self):
        """Test serializer with OTP that's too long"""
        data = {"email": "<EMAIL>", "otp": "1234567"}  # 7 digits instead of 6

        serializer = OTPVerificationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "otp" in serializer.errors


@pytest.mark.django_db
class TestBusinessUserRegistrationSerializer:
    """Tests for the BusinessUserRegistrationSerializer"""

    def test_valid_data(self):
        """Test serializer with valid data"""
        data = {
            "business_name": "Test Business",
            "firstname": "Glory",
            "lastname": "Kolade",
            "email": "<EMAIL>",
            "phone": "1234567890",
        }

        serializer = BusinessUserRegistrationSerializer(data=data)

        assert serializer.is_valid() is True
        assert serializer.validated_data["business_name"] == "Test Business"
        assert serializer.validated_data["firstname"] == "Glory"
        assert serializer.validated_data["lastname"] == "Kolade"
        assert serializer.validated_data["email"] == "<EMAIL>"
        assert serializer.validated_data["phone"] == "1234567890"

    def test_missing_business_name(self):
        """Test serializer with missing business name"""
        data = {
            "firstname": "Glory",
            "lastname": "Kolade",
            "email": "<EMAIL>",
            "phone": "1234567890",
        }

        serializer = BusinessUserRegistrationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "business_name" in serializer.errors

    def test_existing_email(self, create_user):
        """Test serializer with existing email"""
        # Create a user with the email we'll try to register
        create_user(email="<EMAIL>")

        data = {
            "business_name": "Test Business",
            "firstname": "Glory",
            "lastname": "Kolade",
            "email": "<EMAIL>",
            "phone": "1234567890",
        }

        serializer = BusinessUserRegistrationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors

    def test_invalid_email_format(self):
        """Test serializer with invalid email format"""
        data = {
            "business_name": "Test Business",
            "firstname": "Glory",
            "lastname": "Kolade",
            "email": "invalid-email",
            "phone": "1234567890",
        }

        serializer = BusinessUserRegistrationSerializer(data=data)

        assert serializer.is_valid() is False
        assert "email" in serializer.errors


@pytest.mark.django_db
class TestCreatePasswordSerializer:
    """Tests for the CreatePasswordSerializer"""

    def test_valid_data(self):
        """Test serializer with valid data"""
        data = {"token": "valid-token", "password": "NewPassword123!"}

        serializer = CreatePasswordSerializer(data=data)

        assert serializer.is_valid() is True
        assert serializer.validated_data["token"] == "valid-token"
        assert serializer.validated_data["password"] == "NewPassword123!"

    def test_missing_token(self):
        """Test serializer with missing token"""
        data = {"password": "NewPassword123!"}

        serializer = CreatePasswordSerializer(data=data)

        assert serializer.is_valid() is False
        assert "token" in serializer.errors

    def test_missing_password(self):
        """Test serializer with missing password"""
        data = {"token": "valid-token"}

        serializer = CreatePasswordSerializer(data=data)

        assert serializer.is_valid() is False
        assert "password" in serializer.errors
