from datetime import timed<PERSON><PERSON>

import pytest
from django.utils import timezone
from user.models import User


@pytest.mark.django_db
class TestUserModel:
    """Tests for the User model"""

    def test_create_user(self, create_user):
        """Test creating a user with valid data"""
        user = create_user()

        assert user.email == "<EMAIL>"
        assert user.firstname == "Test"
        assert user.lastname == "User"
        assert user.role == "Initiator"
        assert user.verified is True
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False

    def test_create_user_with_invalid_role(self):
        """Test creating a user with invalid role raises error"""
        with pytest.raises(ValueError):
            User.objects.create_user(
                email="<EMAIL>",
                password="password123",
                firstname="Invalid",
                lastname="Role",
                role="InvalidRole",  # This is not a valid role
            )

    def test_user_fullname_property(self, create_user):
        """Test the fullname property returns correct value"""
        user = create_user(firstname="<PERSON>", lastname="<PERSON><PERSON>")

        assert user.fullname == "<PERSON>"

    def test_save_last_login(self, create_user):
        """Test save_last_login method updates last_login field"""
        user = create_user()
        assert user.last_login is None

        user.save_last_login()

        user.refresh_from_db()
        assert user.last_login is not None

    def test_verify_user(self, create_user):
        """Test verify_user method sets verified to True"""
        user = create_user(verified=False)
        assert user.verified is False

        user.verify_user()

        user.refresh_from_db()
        assert user.verified is True

    def test_generate_otp(self, create_user):
        """Test generate_otp method creates a 6-digit OTP"""
        user = create_user()

        otp = user.generate_otp()

        assert len(otp) == 6
        assert otp.isdigit()

        user.refresh_from_db()
        assert user.otp == otp
        assert user.otp_created_at is not None

    def test_verify_otp_valid(self, create_user):
        """Test verify_otp method with valid OTP"""
        user = create_user()
        otp = user.generate_otp()

        assert user.verify_otp(otp) is True

    def test_verify_otp_invalid(self, create_user):
        """Test verify_otp method with invalid OTP"""
        user = create_user()
        user.generate_otp()

        assert user.verify_otp("999999") is False

    def test_verify_otp_expired(self, create_user):
        """Test verify_otp method with expired OTP"""
        user = create_user()
        otp = user.generate_otp()

        # Set OTP creation time to 11 minutes ago (expired)
        user.otp_created_at = timezone.now() - timedelta(minutes=11)
        user.save()

        assert user.verify_otp(otp) is False

    def test_verify_otp_no_otp(self, create_user):
        """Test verify_otp method with no OTP set"""
        user = create_user()

        assert user.verify_otp("123456") is False


@pytest.mark.django_db
class TestTokenModel:
    """Tests for the Token model"""

    def test_create_token(self, create_user, create_token):
        """Test creating a token with valid data"""
        user = create_user()
        token = create_token(user)

        assert token.user == user
        assert token.token == f"test-token-{user.id}"
        assert token.token_type == "CreateToken"
        assert token.created_at is not None

    def test_token_string_representation(self, create_user, create_token):
        """Test the string representation of a token"""
        user = create_user()
        token = create_token(user)

        assert str(token) == f"{str(user)} {token.token}"

    def test_token_is_valid_not_expired(self, create_user, create_token, settings):
        """Test is_valid method returns True for non-expired token"""
        # Set token lifespan to 24 hours for testing
        settings.TOKEN_LIFESPAN = 24

        user = create_user()
        token = create_token(user)

        assert token.is_valid() is True

    def test_token_is_valid_expired(self, create_user, create_token, settings):
        """Test is_valid method returns False for expired token"""
        # Set token lifespan to 1 hour for testing
        settings.TOKEN_LIFESPAN = 1

        user = create_user()
        token = create_token(user)

        # Set token creation time to 2 hours ago (expired)
        token.created_at = timezone.now() - timedelta(hours=2)
        token.save()

        assert token.is_valid() is False
