import uuid

import pyotp
import pytest
from common.security import CipherSuite
from django.urls import reverse
from rest_framework.exceptions import PermissionDenied, ValidationError
from user.models import User
from user.utils import cache_user_session_key, get_user_session_key


@pytest.mark.django_db
class TestTwoFactorAuthMethods:
    """Unit tests for 2FA user methods"""

    def test_generate_2fa_secret_length(self, create_user):
        user = create_user()
        secret = user.generate_2fa_secret()
        assert isinstance(secret, str)
        assert len(secret) >= 16

    def test_encrypt_and_decrypt_2fa_secret(self):
        secret = "ABC123SECRETKEY"
        encrypted = CipherSuite.encrypt_secret(secret)
        decrypted = CipherSuite.decrypt_secret(encrypted)
        assert decrypted == secret
        assert encrypted != secret

    def test_generate_qrcode_sets_encrypted_secret(self, create_user):
        user = create_user(two_factor_auth_enabled=False)
        qr_code = user.generate_qrcode()

        user.refresh_from_db()
        assert qr_code is not None
        assert user.two_factor_auth_secret is not None
        assert isinstance(user.two_factor_auth_secret, str)

    def test_plain_two_factor_auth_secret_property(self, create_user):
        secret = "TEST2FASECRET"
        user = create_user()
        user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
        user.save()

        assert user.plain_two_factor_auth_secret == secret

    def test_generate_base64_qrcode_returns_uri_and_qr(self, create_user):
        secret = pyotp.random_base32()
        user = create_user()
        user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
        user.save()

        otp_auth_url, base64_img = user.generate_base64_qrcode()

        assert "otpauth://" in otp_auth_url
        assert base64_img.startswith("iVBOR")  # PNG base64 prefix

    def test_validate_two_factor_auth_otp_success(self, create_user):
        secret = pyotp.random_base32()
        user = create_user()
        user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
        user.save()

        valid_otp = pyotp.TOTP(secret).now()
        user.validate_two_factor_auth_otp(valid_otp)

    def test_validate_two_factor_auth_otp_invalid(self, create_user):
        secret = pyotp.random_base32()
        user = create_user()
        user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
        user.save()

        with pytest.raises(ValidationError) as exc:
            user.validate_two_factor_auth_otp("000000")

        assert "2FA Code is invalid" in str(exc.value)

    def test_validate_two_factor_auth_otp_not_setup(self, create_user):
        user = create_user(two_factor_auth_secret=None)

        with pytest.raises(PermissionDenied) as exc:
            user.validate_two_factor_auth_otp("123456")

        assert "2FA not setup" in str(exc.value)


@pytest.fixture
def test_user(db):
    user = User.objects.create_user(
        email="<EMAIL>",
        password="testpass123",
        verified=True,
        role="Business_Owner",
    )
    return user


def get_otp(secret):
    return pyotp.TOTP(secret).now()


def test_2fa_login_valid_credentials(api_client, test_user):
    url = reverse("auth:2fa-login")
    data = {
        "email": test_user.email,
        "password": "testpass123",
    }
    response = api_client.post(url, data)
    assert response.status_code == 200
    assert "session_id" in response.data["data"]
    assert "two_factor_auth_enabled" in response.data["data"]
    assert response.data["data"]["two_factor_auth_enabled"] is False
    assert response.data["data"]["session_id"] is not None
    assert response.data["data"]["session_id"] == get_user_session_key(test_user.id)


def test_2fa_login_invalid_credentials(api_client, test_user):
    url = reverse("auth:2fa-login")
    data = {
        "email": test_user.email,
        "password": "wrongpassword",
    }
    response = api_client.post(url, data)
    assert response.status_code == 400
    assert response.data["email"][0] == "Invalid login credentials"


def test_2fa_setup_valid(api_client, test_user):
    session_id = cache_user_session_key(test_user.id)
    url = reverse("auth:2fa-setup")
    data = {
        "email": test_user.email,
        "session_id": session_id,
        "two_factor_auth_type": "TOTP",
    }
    response = api_client.post(url, data)
    assert response.status_code == 200
    assert "qr_code" in response.data["data"]


def test_2fa_setup_invalid_session(api_client, test_user):
    wrong_session = str(uuid.uuid4().hex)
    url = reverse("auth:2fa-setup")
    data = {
        "email": test_user.email,
        "session_id": wrong_session,
        "two_factor_auth_type": "TOTP",
    }
    response = api_client.post(url, data)
    assert response.status_code == 400
    assert "email" in response.data or "session" in response.data


@pytest.mark.django_db
def test_2fa_finalize_valid(api_client, test_user):
    # Simulate 2FA setup first
    secret = pyotp.random_base32()
    test_user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
    test_user.two_factor_auth_type = "TOTP"
    test_user.save()

    session_id = cache_user_session_key(test_user.id)
    otp = get_otp(secret)

    url = reverse("auth:2fa-finalize")
    data = {
        "email": test_user.email,
        "session_id": session_id,
        "otp": otp,
    }
    response = api_client.post(url, data)
    assert response.status_code == 200
    assert "access" in response.data["data"]
    assert "refresh" in response.data["data"]


@pytest.mark.django_db
def test_2fa_finalize_invalid_otp(api_client, test_user):
    secret = pyotp.random_base32()
    test_user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
    test_user.two_factor_auth_type = "TOTP"
    test_user.save()

    session_id = cache_user_session_key(test_user.id)

    url = reverse("auth:2fa-finalize")
    data = {
        "email": test_user.email,
        "session_id": session_id,
        "otp": "000000",  # Invalid OTP
    }
    response = api_client.post(url, data)
    assert response.status_code == 400
    assert "otp" in response.data


@pytest.mark.django_db
def test_2fa_finalize_invalid_session(api_client, test_user):
    secret = pyotp.random_base32()
    test_user.two_factor_auth_secret = CipherSuite.encrypt_secret(secret)
    test_user.two_factor_auth_type = "TOTP"
    test_user.save()

    url = reverse("auth:2fa-finalize")
    data = {
        "email": test_user.email,
        "session_id": str(uuid.uuid4().hex),  # Invalid session
        "otp": get_otp(secret),
    }
    response = api_client.post(url, data)
    assert response.status_code == 400
    assert "email" in response.data or "session" in response.data
