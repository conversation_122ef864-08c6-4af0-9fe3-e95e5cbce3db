import pytest
from rest_framework.test import APIClient
from user.models import Token, User


@pytest.fixture
def api_client():
    """Return an API client for testing"""
    return APIClient()


@pytest.fixture
def create_user():
    """Factory fixture to create users with default values"""

    def _create_user(**kwargs):
        defaults = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "firstname": "Test",
            "lastname": "User",
            "role": "Initiator",
            "verified": True,
        }
        defaults.update(kwargs)

        user = User.objects.create_user(**defaults)
        return user

    return _create_user


@pytest.fixture
def create_token():
    """Factory fixture to create tokens with default values"""

    def _create_token(user, token_type="CreateToken"):
        token = Token.objects.create(
            user=user, token=f"test-token-{user.id}", token_type=token_type
        )
        return token

    return _create_token


@pytest.fixture
def authenticated_client(api_client, create_user):
    """Return an authenticated API client"""
    user = create_user()
    api_client.force_authenticate(user=user)
    return api_client, user
