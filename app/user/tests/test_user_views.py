from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

import pytest
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from user.models import Token, User


@pytest.mark.django_db
class TestLoginRequest:
    """Tests for the login_request endpoint"""

    def test_login_request_success(self, api_client, create_user):
        """Test successful login request with valid credentials"""
        user = create_user()

        url = reverse("user:user-login-request")

        with patch("user.v1.views.send_login_otp_email.delay") as mock_send_email:
            response = api_client.post(
                url, {"email": user.email, "password": "TestPassword123!"}
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.data["success"] is True
            assert "OTP sent to your email" in response.data["message"]
            assert response.data["email"] == user.email

            # Verify OTP was generated and email was sent
            mock_send_email.assert_called_once()

            # Refresh user from database
            user.refresh_from_db()
            assert user.otp is not None
            assert user.otp_created_at is not None

    def test_login_request_invalid_credentials(self, api_client, create_user):
        """Test login request with invalid credentials"""
        user = create_user()

        url = reverse("user:user-login-request")

        with patch("user.v1.views.send_login_otp_email.delay") as mock_send_email:
            response = api_client.post(
                url, {"email": user.email, "password": "WrongPassword123!"}
            )

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            assert response.data["success"] is False
            assert "Invalid credentials" in response.data["message"]

            # Verify email was not sent
            mock_send_email.assert_not_called()

    def test_login_request_inactive_user(self, api_client, create_user):
        """Test login request with inactive user"""
        user = create_user(is_active=False)

        url = reverse("user:user-login-request")

        with patch("user.v1.views.send_login_otp_email.delay") as mock_send_email:
            response = api_client.post(
                url, {"email": user.email, "password": "TestPassword123!"}
            )

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            assert response.data["success"] is False

            # Verify email was not sent
            mock_send_email.assert_not_called()


@pytest.mark.django_db
class TestVerifyLogin:
    """Tests for the verify_login endpoint"""

    def test_verify_login_success(self, api_client, create_user):
        """Test successful OTP verification"""
        user = create_user()
        otp = user.generate_otp()

        url = reverse("user:user-verify-login")

        response = api_client.post(url, {"email": user.email, "otp": otp})

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "Login successful" in response.data["message"]
        assert "refresh" in response.data
        assert "access" in response.data
        assert "user" in response.data
        assert response.data["user"]["email"] == user.email

        # Verify OTP was cleared
        user.refresh_from_db()
        assert user.otp is None
        assert user.otp_created_at is None
        assert user.last_login is not None

    def test_verify_login_invalid_otp(self, api_client, create_user):
        """Test OTP verification with invalid OTP"""
        user = create_user()
        user.generate_otp()

        url = reverse("user:user-verify-login")

        response = api_client.post(
            url, {"email": user.email, "otp": "999999"}  # Wrong OTP
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "Invalid or expired OTP" in response.data["message"]

    def test_verify_login_expired_otp(self, api_client, create_user):
        """Test OTP verification with expired OTP"""
        user = create_user()
        otp = user.generate_otp()

        # Set OTP creation time to 11 minutes ago (expired)
        user.otp_created_at = timezone.now() - timedelta(minutes=11)
        user.save()

        url = reverse("user:user-verify-login")

        response = api_client.post(url, {"email": user.email, "otp": otp})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "Invalid or expired OTP" in response.data["message"]

    def test_verify_login_user_not_found(self, api_client):
        """Test OTP verification with non-existent user"""
        url = reverse("user:user-verify-login")

        response = api_client.post(
            url, {"email": "<EMAIL>", "otp": "123456"}
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data["success"] is False
        assert "User not found" in response.data["message"]


@pytest.mark.django_db
class TestRegisterBusiness:
    """Tests for the register_business endpoint"""

    def test_register_business_success(self, api_client):
        """Test successful business registration"""
        url = reverse("user:user-register-business")

        with patch("user.v1.views.send_registration_email.delay") as mock_send_email:
            response = api_client.post(
                url,
                {
                    "business_name": "Test Business",
                    "firstname": "Glory",
                    "lastname": "Kolade",
                    "email": "<EMAIL>",
                    "phone": "**********",
                },
            )

            assert response.status_code == status.HTTP_201_CREATED
            assert response.data["success"] is True
            assert "Registration successful" in response.data["message"]
            assert response.data["email"] == "<EMAIL>"

            # Verify user was created
            user = User.objects.get(email="<EMAIL>")
            assert user.firstname == "Glory"
            assert user.lastname == "Kolade"
            assert user.role == "Business_Owner"
            assert not user.verified

            # Verify token was created
            token = Token.objects.get(user=user)
            assert token.token_type == "CreateToken"

            # Verify email was sent
            mock_send_email.assert_called_once()

    def test_register_business_existing_email(self, api_client, create_user):
        """Test business registration with existing email"""
        # Create a user with the email we'll try to register
        create_user(email="<EMAIL>")

        url = reverse("user:user-register-business")

        response = api_client.post(
            url,
            {
                "business_name": "Test Business",
                "firstname": "Glory",
                "lastname": "Kolade",
                "email": "<EMAIL>",
                "phone": "**********",
            },
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "already exists" in str(response.data)


@pytest.mark.django_db
class TestCreateAccountPassword:
    """Tests for the create_account_password endpoint"""

    def test_create_account_password_success(
        self, api_client, create_user, create_token
    ):
        """Test successful account password creation"""
        user = create_user(verified=False)
        token = create_token(user, token_type="CreateToken")

        url = reverse("user:user-create-account-password")

        response = api_client.post(
            url, {"token": token.token, "password": "NewPassword123!"}
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "Account setup completed successfully" in response.data["message"]
        assert "refresh" in response.data
        assert "access" in response.data
        assert "user" in response.data
        assert response.data["user"]["email"] == user.email

        # Verify user was updated
        user.refresh_from_db()
        assert user.verified is True
        assert user.last_login is not None

        # Verify token was deleted
        assert not Token.objects.filter(id=token.id).exists()

    def test_create_account_password_invalid_token(self, api_client):
        """Test account password creation with invalid token"""
        url = reverse("user:user-create-account-password")

        response = api_client.post(
            url, {"token": "invalid-token", "password": "NewPassword123!"}
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "Invalid or expired token" in response.data["errors"]

    def test_create_account_password_expired_token(
        self, api_client, create_user, create_token, settings
    ):
        """Test account password creation with expired token"""
        # Set token lifespan to 1 hour for testing
        settings.TOKEN_LIFESPAN = 1

        user = create_user(verified=False)
        token = create_token(user, token_type="CreateToken")

        # Set token creation time to 2 hours ago (expired)
        token.created_at = timezone.now() - timedelta(hours=2)
        token.save()

        url = reverse("user:user-create-account-password")

        response = api_client.post(
            url, {"token": token.token, "password": "NewPassword123!"}
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "Invalid or expired token" in response.data["errors"]
