import time
import uuid

from django.conf import settings
from django.core.cache import cache
from django.core.files import File
from django.core.mail import EmailMultiAlternatives
from django.utils.crypto import get_random_string


def send_email(subject, recipient, html_alternative, text_alternative):
    msg = EmailMultiAlternatives(
        subject, text_alternative, settings.EMAIL_FROM, [recipient]
    )
    msg.attach_alternative(html_alternative, "text/html")
    msg.send()


async def create_file_from_image(url):
    return File(open(url, "rb"))


def generate_token(user):
    return get_random_string(120) + str(user.id) + str(time.time())[:6]


def cache_user_session_key(user_id: str, ttl=86400) -> str:
    session_key = f"user_session_{user_id}"
    cache.delete(f"user_session_{user_id}", None)
    session_id = str(uuid.uuid4().hex)
    cache.set(session_key, session_id, ttl)
    return session_id


def get_or_create_user_session_key(user_id: str):
    cache_obj = cache.get(f"user_session_{user_id}", None)
    cache_obj = cache_user_session_key(user_id) if not cache_obj else cache_obj
    return cache_obj


def get_user_session_key(user_id: str):
    cache_obj = cache.get(f"user_session_{user_id}", None)
    return cache_obj
