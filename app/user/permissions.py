from rest_framework import permissions


class IsSuperAdmin(permissions.BasePermission):
    """Allows access only to super admin users."""

    message = "Only Super Admins are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user and request.user.roles and "SUPERADMIN" in request.user.roles
        )


class IsAdmin(permissions.BasePermission):
    """Allows access only to admin users."""

    message = "Only Admins are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user and request.user.roles and "ADMIN" in request.user.roles
        )


class IsRegularUser(permissions.BasePermission):
    """Allows access only to talent users."""

    message = "Only Regular users are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user and request.user.roles and "REGULAR" in request.user.roles
        )
