import base64
import random
import uuid
from datetime import datetime, timedelta, timezone as dt_timezone
from io import BytesIO

import pyotp
import qrcode
from common.kgs import generate_unique_id
from common.security import CipherSuite
from django.conf import settings
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.utils.timezone import make_aware
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import PermissionDenied, ValidationError

from .enums import ROLE_OPTIONS, TOKEN_TYPE, Auth2FATypeEnums
from .managers import CustomUserManager, TokenManager


class User(AbstractBaseUser, PermissionsMixin, Auth2FATypeEnums):
    id = models.CharField(
        max_length=50, primary_key=True, default=generate_unique_id, editable=False
    )
    email = models.EmailField(_("email address"), unique=True, db_index=True)
    password = models.Char<PERSON><PERSON>(max_length=600, null=True)
    transaction_pin = models.CharField(max_length=400, null=True)
    firstname = models.CharField(max_length=255)
    lastname = models.CharField(max_length=255)
    phone = models.CharField(max_length=17, blank=True, null=True)
    image = models.FileField(upload_to="users/", blank=True, null=True)
    role = models.CharField(max_length=100, choices=ROLE_OPTIONS)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    verified = models.BooleanField(default=False)
    otp = models.CharField(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)
    two_factor_auth_secret = models.TextField(blank=True, null=True)
    two_factor_auth_enabled = models.BooleanField(default=False)
    two_factor_auth_type = models.CharField(
        max_length=100, choices=Auth2FATypeEnums.CHOICES, default=Auth2FATypeEnums.TOTP
    )
    is_password_set = models.BooleanField(default=False)
    password_last_changed_at = models.DateTimeField(null=True, blank=True)
    google_sub = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        unique=True,
        help_text="Google user ID (sub).",
        db_index=True,
    )
    is_google_account_linked = models.BooleanField(
        default=False, help_text="Whether this user has linked their Google account."
    )

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    class Meta:
        ordering = ("lastname", "firstname")

    def __str__(self):
        return self.email

    @property
    def fullname(self):
        return f"{self.firstname} {self.lastname}"

    @property
    def plain_two_factor_auth_secret(self):
        decrypted_secret = CipherSuite.decrypt_secret(self.two_factor_auth_secret)
        return decrypted_secret

    def save(self, *args, **kwargs):
        if self.role not in dict(ROLE_OPTIONS):
            raise ValueError(
                f"Invalid role: {self.role}. Must be one of {dict(ROLE_OPTIONS).keys()}."
            )
        super().save(*args, **kwargs)

    def save_last_login(self):
        self.last_login = make_aware(datetime.now())
        self.save()

    def verify_user(self):
        self.verified = True
        self.save()

    def generate_otp(self):
        """Generate a 6-digit OTP and save it to the user"""
        self.otp = "".join([str(random.randint(0, 9)) for _ in range(6)])
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        """Verify if the provided OTP is valid and not expired"""
        if not self.otp or not self.otp_created_at:
            return False

        # OTP expires after 10 minutes
        expiry_time = self.otp_created_at + timedelta(minutes=10)
        if timezone.now() > expiry_time:
            return False

        return self.otp == otp

    def generate_2fa_secret(self) -> str:
        return pyotp.random_base32()

    def generate_base64_qrcode(self) -> tuple[str, str]:
        otp_auth_url = pyotp.totp.TOTP(
            s=self.plain_two_factor_auth_secret
        ).provisioning_uri(name=self.email.lower(), issuer_name="SageCloud")
        image = qrcode.make(f"{otp_auth_url}")
        buffer = BytesIO()

        image.save(buffer, format="PNG")
        buffer.seek(0)

        img_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        buffer.close()

        return otp_auth_url, img_base64

    def generate_qrcode(self) -> str:
        if self.two_factor_auth_enabled:
            return None
        two_factor_auth_secret = self.generate_2fa_secret()
        self.two_factor_auth_secret = CipherSuite.encrypt_secret(two_factor_auth_secret)
        self.save(update_fields=["two_factor_auth_secret"])
        _, qr_code = self.generate_base64_qrcode()
        return qr_code

    def validate_two_factor_auth_otp(self, otp):
        if not self.two_factor_auth_secret:
            raise PermissionDenied("2FA not setup.")
        totp = pyotp.TOTP(self.plain_two_factor_auth_secret).now()
        if str(totp) != str(otp):
            raise ValidationError({"auth": "2FA Code is invalid or expired."})


class Token(models.Model):
    objects = TokenManager()

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, null=True)
    token_type = models.CharField(
        max_length=100, choices=TOKEN_TYPE, default="CreateToken"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{str(self.user)} {self.token}"

    def is_valid(self):
        lifespan_in_seconds = float(settings.TOKEN_LIFESPAN * 60 * 60)
        now = datetime.now(dt_timezone.utc)
        time_diff = now - self.created_at
        time_diff = time_diff.total_seconds()
        if time_diff >= lifespan_in_seconds:
            return False
        return True
