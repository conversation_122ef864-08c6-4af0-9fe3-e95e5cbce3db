from unittest.mock import patch

from business.models import Business
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from .models import ExportField, ExportRequest
from .services import ExportFieldManager, ExportService
from .utils import format_file_size, validate_date_range

User = get_user_model()


class ExportModelTests(TestCase):
    """Test cases for Export models"""

    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            firstname="Test",
            lastname="User",
            role="Business_Owner",
        )
        self.business = Business.objects.create(name="Test Business", owner=self.user)

    def test_export_request_creation(self):
        """Test creating an export request"""
        export_request = ExportRequest.objects.create(
            user=self.user,
            business=self.business,
            export_type=ExportRequest.ExportType.TRANSACTIONS,
            model_name="transaction.Transaction",
            fields_to_export=["id", "reference", "amount"],
            filename="test_export.csv",
        )

        self.assertEqual(export_request.status, ExportRequest.Status.PENDING)
        self.assertEqual(export_request.progress_percentage, 0)
        self.assertFalse(export_request.is_expired)

    def test_export_field_creation(self):
        """Test creating export fields"""
        field = ExportField.objects.create(
            model_name="transaction.Transaction",
            field_name="id",
            field_label="Transaction ID",
            field_type="CharField",
            is_default=True,
            order=1,
        )

        self.assertEqual(str(field), "transaction.Transaction.id")


class ExportServiceTests(TestCase):
    """Test cases for ExportService"""

    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            firstname="Test",
            lastname="User",
            role="Business_Owner",
        )
        self.business = Business.objects.create(name="Test Business", owner=self.user)
        self.service = ExportService()

    @patch("exports.services.ExportService._get_s3_client")
    def test_create_export_request(self, mock_s3):
        """Test creating export request through service"""
        # Setup export fields
        ExportFieldManager.setup_model_fields(
            "transaction.Transaction",
            [{"field_name": "id", "field_label": "ID", "is_default": True, "order": 1}],
        )

        export_request = self.service.create_export_request(
            user=self.user,
            business=self.business,
            model_name="transaction.Transaction",
            export_type="transactions",
            fields_to_export=["id"],
        )

        self.assertIsInstance(export_request, ExportRequest)
        self.assertEqual(export_request.user, self.user)
        self.assertEqual(export_request.business, self.business)


class ExportUtilsTests(TestCase):
    """Test cases for export utilities"""

    def test_validate_date_range_valid(self):
        """Test valid date range validation"""
        result = validate_date_range("2023-01-01T00:00:00", "2023-12-31T23:59:59")
        self.assertTrue(result["valid"])
        self.assertEqual(len(result["errors"]), 0)

    def test_validate_date_range_invalid(self):
        """Test invalid date range validation"""
        result = validate_date_range("2023-12-31T23:59:59", "2023-01-01T00:00:00")
        self.assertFalse(result["valid"])
        self.assertIn("date_from cannot be later than date_to", result["errors"])

    def test_format_file_size(self):
        """Test file size formatting"""
        self.assertEqual(format_file_size(0), "0 B")
        self.assertEqual(format_file_size(1024), "1.0 KB")
        self.assertEqual(format_file_size(1024 * 1024), "1.0 MB")


class ExportAPITests(APITestCase):
    """Test cases for Export API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            firstname="Test",
            lastname="User",
            role="Business_Owner",
        )
        self.business = Business.objects.create(name="Test Business", owner=self.user)
        self.client.force_authenticate(user=self.user)

        # Setup export fields
        ExportFieldManager.setup_model_fields(
            "transaction.Transaction",
            [
                {
                    "field_name": "id",
                    "field_label": "ID",
                    "is_default": True,
                    "order": 1,
                },
                {
                    "field_name": "reference",
                    "field_label": "Reference",
                    "is_default": True,
                    "order": 2,
                },
            ],
        )

    def test_get_supported_models(self):
        """Test getting supported models"""
        url = reverse("export-supported-models")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("supported_models", response.data)

    @patch("exports.tasks.process_export_task.delay")
    def test_create_export_request(self, mock_task):
        """Test creating export request via API"""
        url = reverse("export-list")
        data = {
            "export_type": "transactions",
            "model_name": "transaction.Transaction",
            "fields_to_export": ["id", "reference"],
            "filters": {
                "date_from": "2023-01-01T00:00:00",
                "date_to": "2023-12-31T23:59:59",
            },
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(mock_task.called)

        # Verify export request was created
        export_request = ExportRequest.objects.get(id=response.data["id"])
        self.assertEqual(export_request.user, self.user)
        self.assertEqual(export_request.business, self.business)

    def test_get_available_fields(self):
        """Test getting available fields for a model"""
        url = reverse("export-available-fields")
        data = {"model_name": "transaction.Transaction"}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("available_fields", response.data)
        self.assertIn("default_fields", response.data)

    def test_export_request_list(self):
        """Test listing export requests"""
        # Create a test export request
        ExportRequest.objects.create(
            user=self.user,
            business=self.business,
            export_type=ExportRequest.ExportType.TRANSACTIONS,
            model_name="transaction.Transaction",
            fields_to_export=["id"],
            filename="test.csv",
        )

        url = reverse("export-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)

    def test_unauthorized_access(self):
        """Test unauthorized access to export endpoints"""
        self.client.force_authenticate(user=None)

        url = reverse("export-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
