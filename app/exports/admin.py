from django.contrib import admin

from .models import ExportField, ExportRequest


@admin.register(ExportRequest)
class ExportRequestAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "business",
        "export_type",
        "model_name",
        "status",
        "total_records",
        "progress_percentage",
        "created_at",
        "expires_at",
    ]
    list_filter = ["status", "export_type", "created_at", "email_sent"]
    search_fields = ["user__email", "business__name", "filename"]
    readonly_fields = [
        "created_at",
        "updated_at",
        "progress_percentage",
        "file_size",
        "processed_records",
    ]

    fieldsets = (
        ("Request Info", {"fields": ("user", "business", "export_type", "model_name")}),
        ("Configuration", {"fields": ("fields_to_export", "filters")}),
        (
            "File Details",
            {"fields": ("filename", "file_path", "file_size", "download_url")},
        ),
        (
            "Status",
            {
                "fields": (
                    "status",
                    "total_records",
                    "processed_records",
                    "error_message",
                )
            },
        ),
        (
            "Email & Expiration",
            {"fields": ("email_sent", "email_sent_at", "expires_at")},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(ExportField)
class ExportFieldAdmin(admin.ModelAdmin):
    list_display = [
        "model_name",
        "field_name",
        "field_label",
        "field_type",
        "is_default",
        "is_sensitive",
        "order",
    ]
    list_filter = ["model_name", "field_type", "is_default", "is_sensitive"]
    search_fields = ["model_name", "field_name", "field_label"]
    list_editable = ["is_default", "is_sensitive", "order"]
    ordering = ["model_name", "order", "field_name"]
