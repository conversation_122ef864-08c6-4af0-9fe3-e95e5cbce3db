# Generated by Django 5.1.7 on 2025-06-22 18:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("exports", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="exportrequest",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="export_requests",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="exportrequest",
            index=models.Index(
                fields=["user", "-created_at"], name="exports_exp_user_id_f20cc9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="exportrequest",
            index=models.Index(
                fields=["business", "-created_at"],
                name="exports_exp_busines_1ee460_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="exportrequest",
            index=models.Index(
                fields=["status", "-created_at"], name="exports_exp_status_84a492_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="exportrequest",
            index=models.Index(
                fields=["export_type", "-created_at"],
                name="exports_exp_export__929373_idx",
            ),
        ),
    ]
