import random
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from audit.models import AuditLog
from business.models import Business
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.utils import timezone
from transaction.models import Transaction
from wallet.models import Wallet

User = get_user_model()


class Command(BaseCommand):
    help = "Seed test data for export functionality testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--transactions",
            type=int,
            default=50,
            help="Number of test transactions to create (default: 50)",
        )
        parser.add_argument(
            "--audit-logs",
            type=int,
            default=30,
            help="Number of test audit logs to create (default: 30)",
        )
        parser.add_argument(
            "--user-email",
            type=str,
            help="Email of existing user to create data for (optional)",
        )

    def handle(self, *args, **options):
        self.stdout.write("🌱 Seeding test data for export testing...")

        # Get or create test user and business
        user, business = self.get_or_create_test_user_business(
            options.get("user_email")
        )

        # Create test transactions
        transaction_count = options["transactions"]
        if transaction_count > 0:
            self.create_test_transactions(user, business, transaction_count)

        # Create test audit logs
        audit_count = options["audit_logs"]
        if audit_count > 0:
            self.create_test_audit_logs(user, audit_count)

        self.stdout.write(self.style.SUCCESS("\n🎉 Test data seeding completed!"))
        self.stdout.write(f"✓ User: {user.email}")
        self.stdout.write(f"✓ Business: {business.name}")
        self.stdout.write(f"✓ Transactions: {transaction_count}")
        self.stdout.write(f"✓ Audit Logs: {audit_count}")
        self.stdout.write("\nYou can now test the export endpoints in Swagger!")

    def get_or_create_test_user_business(self, user_email=None):
        """Get or create test user and business"""
        if user_email:
            try:
                user = User.objects.get(email=user_email)
                business = user.business
                self.stdout.write(f"✓ Using existing user: {user.email}")
                return user, business
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"User with email {user_email} not found")
                )
                self.stdout.write("Creating new test user...")

        # Create test user
        user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "firstname": "Export",
                "lastname": "Tester",
                "role": "Business_Owner",
                "verified": True,
                "is_password_set": True,
            },
        )

        if created:
            self.stdout.write("✓ Created test user: <EMAIL>")
        else:
            self.stdout.write("✓ Using existing test user: <EMAIL>")

        # Create test business
        business, created = Business.objects.get_or_create(
            owner=user,
            defaults={
                "name": "Export Test Business",
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "office_address": "123 Test Street",
                "city": "Test City",
                "state": "Test State",
            },
        )

        if created:
            self.stdout.write("✓ Created test business: Export Test Business")
            # Create wallets for the business
            business._create_core_wallets()
        else:
            self.stdout.write("✓ Using existing test business: Export Test Business")

        return user, business

    def create_test_transactions(self, user, business, count):
        """Create test transactions"""
        self.stdout.write(f"\n📊 Creating {count} test transactions...")

        # Get business wallets
        general_wallet = business.get_general_wallet()

        # Transaction types and statuses
        txn_classes = ["AIRTIME", "DATA", "ELECTRICITY", "CABLE_TV", "BETTING"]
        statuses = ["COMPLETED", "PENDING", "FAILED"]
        modes = ["DEBIT", "CREDIT"]

        transactions_created = 0

        for i in range(count):
            try:
                # Random transaction data
                txn_class = random.choice(txn_classes)
                status = random.choice(statuses)
                mode = random.choice(modes)
                amount = Decimal(str(random.uniform(100, 10000))).quantize(
                    Decimal("0.01")
                )
                charge = Decimal(str(random.uniform(10, 100))).quantize(Decimal("0.01"))
                revenue = charge * Decimal("0.1")  # 10% revenue

                # Random date within last 6 months
                days_ago = random.randint(1, 180)
                created_date = timezone.now() - timedelta(days=days_ago)

                Transaction.objects.create(
                    wallet=general_wallet,
                    business=business,
                    reference=f'TXN_{timezone.now().strftime("%Y%m%d")}_{i+1:04d}',
                    merchant_reference=f"MERCH_{i+1:06d}",
                    status=status,
                    mode=mode,
                    txn_class=txn_class,
                    type=f"{txn_class}_TYPE",
                    charge=charge,
                    revenue=revenue,
                    amount=amount,
                    net_amount=amount - charge,
                    old_balance=Decimal("1000.00"),
                    new_balance=(
                        Decimal("1000.00") + amount
                        if mode == "CREDIT"
                        else Decimal("1000.00") - amount
                    ),
                    narration=f"Test {txn_class.lower()} transaction #{i+1}",
                    is_wallet_impacted=True,
                    created_at=created_date,
                    updated_at=created_date,
                )
                transactions_created += 1

                if (i + 1) % 10 == 0:
                    self.stdout.write(f"   Created {i + 1}/{count} transactions...")

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(
                        f"   Failed to create transaction {i+1}: {str(e)}"
                    )
                )

        self.stdout.write(
            self.style.SUCCESS(f"✓ Created {transactions_created} transactions")
        )

    def create_test_audit_logs(self, user, count):
        """Create test audit logs"""
        self.stdout.write(f"\n📋 Creating {count} test audit logs...")

        # Audit log actions and statuses
        actions = [
            "USER_LOGIN",
            "USER_LOGOUT",
            "TRANSACTION_CREATE",
            "TRANSACTION_UPDATE",
            "BUSINESS_UPDATE",
            "WALLET_CREDIT",
            "WALLET_DEBIT",
            "EXPORT_CREATE",
            "PASSWORD_CHANGE",
            "PROFILE_UPDATE",
        ]
        statuses = ["SUCCESS", "FAILED", "WARNING"]

        audit_logs_created = 0

        for i in range(count):
            try:
                action = random.choice(actions)
                status = random.choice(statuses)

                # Random date within last 3 months
                days_ago = random.randint(1, 90)
                created_date = timezone.now() - timedelta(days=days_ago)

                audit_log = AuditLog.log_action(
                    user=user,
                    email=user.email,
                    action=action,
                    description=f'Test {action.lower().replace("_", " ")} action #{i+1}',
                    ip_address=f"192.168.1.{random.randint(1, 254)}",
                    user_agent="Mozilla/5.0 (Test Browser) Export Test Agent",
                    status=status,
                    resource_type="TEST_RESOURCE",
                    resource_id=f"test_resource_{i+1}",
                    metadata={
                        "test_data": True,
                        "sequence": i + 1,
                        "random_value": random.randint(1, 1000),
                    },
                )

                # Update created_at to our random date
                audit_log.created_at = created_date
                audit_log.save(update_fields=["created_at"])

                audit_logs_created += 1

                if (i + 1) % 10 == 0:
                    self.stdout.write(f"   Created {i + 1}/{count} audit logs...")

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"   Failed to create audit log {i+1}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(f"✓ Created {audit_logs_created} audit logs")
        )

    def get_summary_stats(self, user, business):
        """Get summary statistics"""
        transaction_count = Transaction.objects.filter(business=business).count()
        audit_log_count = AuditLog.objects.filter(user=user).count()
        wallet_count = Wallet.objects.filter(business=business).count()

        return {
            "transactions": transaction_count,
            "audit_logs": audit_log_count,
            "wallets": wallet_count,
        }
