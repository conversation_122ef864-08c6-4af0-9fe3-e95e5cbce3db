from django.core.management.base import BaseCommand
from exports.services import ExportFieldManager


class Command(BaseCommand):
    help = "Setup exportable fields for supported models"

    def add_arguments(self, parser):
        parser.add_argument(
            "--model",
            type=str,
            help="Setup fields for specific model only (e.g., transaction.Transaction)",
        )

    def handle(self, *args, **options):
        self.stdout.write("Setting up exportable fields...")

        specific_model = options.get("model")
        if specific_model:
            self.setup_specific_model(specific_model)
            return

        self.setup_all_models()

    def setup_all_models(self):
        """Setup fields for all supported models"""

        # Setup all models
        try:
            ExportFieldManager.setup_model_fields(
                "transaction.Transaction", self.get_transaction_fields()
            )
            self.stdout.write(self.style.SUCCESS("✓ Transaction fields setup complete"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up transaction fields: {str(e)}")
            )

        try:
            ExportFieldManager.setup_model_fields(
                "audit.AuditLog", self.get_audit_log_fields()
            )
            self.stdout.write(self.style.SUCCESS("✓ Audit Log fields setup complete"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up audit log fields: {str(e)}")
            )

        try:
            ExportFieldManager.setup_model_fields(
                "wallet.Wallet", self.get_wallet_fields()
            )
            self.stdout.write(self.style.SUCCESS("✓ Wallet fields setup complete"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up wallet fields: {str(e)}")
            )

        # Setup VAS transaction models with specific fields
        vas_models = [
            (
                "transaction.AirtimeVASTransaction",
                "Airtime VAS Transaction",
                self.get_airtime_vas_fields,
            ),
            (
                "transaction.DataVASTransaction",
                "Data VAS Transaction",
                self.get_data_vas_fields,
            ),
            (
                "transaction.ElectricityVASTransaction",
                "Electricity VAS Transaction",
                self.get_electricity_vas_fields,
            ),
            (
                "transaction.CableTVVASTransaction",
                "Cable TV VAS Transaction",
                self.get_cable_tv_vas_fields,
            ),
            (
                "transaction.BettingVASTransaction",
                "Betting VAS Transaction",
                self.get_betting_vas_fields,
            ),
            (
                "transaction.EducationVASTransaction",
                "Education VAS Transaction",
                self.get_vas_transaction_fields,
            ),
            (
                "transaction.KYCVASTransaction",
                "KYC VAS Transaction",
                self.get_vas_transaction_fields,
            ),
            (
                "transaction.EpinVASTransaction",
                "Epin VAS Transaction",
                self.get_epin_vas_fields,
            ),
            (
                "transaction.VirtualAccountVasTransaction",
                "Virtual Account VAS Transaction",
                self.get_virtual_account_vas_fields,
            ),
        ]

        for model_name, display_name, field_method in vas_models:
            try:
                ExportFieldManager.setup_model_fields(model_name, field_method())
                self.stdout.write(
                    self.style.SUCCESS(f"✓ {display_name} fields setup complete")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error setting up {display_name} fields: {str(e)}"
                    )
                )

        # Setup fee models for commission rates
        try:
            ExportFieldManager.setup_model_fields(
                "fees.BusinessFee", self.get_business_fee_fields()
            )
            self.stdout.write(
                self.style.SUCCESS("✓ Business Fee fields setup complete")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up business fee fields: {str(e)}")
            )

        try:
            ExportFieldManager.setup_model_fields(
                "fees.VenderFee", self.get_vender_fee_fields()
            )
            self.stdout.write(self.style.SUCCESS("✓ Vender Fee fields setup complete"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up vender fee fields: {str(e)}")
            )

        self.stdout.write(
            self.style.SUCCESS("All export fields setup completed successfully!")
        )
        self.stdout.write(
            "You can now use the export API to export data from these models."
        )
        self.stdout.write(
            "Available models: transaction.Transaction, audit.AuditLog, wallet.Wallet, VAS transactions, and fee models"
        )

    def setup_specific_model(self, model_name):
        """Setup fields for a specific model"""
        model_configs = {
            "transaction.Transaction": self.get_transaction_fields(),
            "audit.AuditLog": self.get_audit_log_fields(),
            "wallet.Wallet": self.get_wallet_fields(),
            "transaction.AirtimeVASTransaction": self.get_airtime_vas_fields(),
            "transaction.DataVASTransaction": self.get_data_vas_fields(),
            "transaction.ElectricityVASTransaction": self.get_electricity_vas_fields(),
            "transaction.CableTVVASTransaction": self.get_cable_tv_vas_fields(),
            "transaction.BettingVASTransaction": self.get_betting_vas_fields(),
            "transaction.EducationVASTransaction": self.get_vas_transaction_fields(),
            "transaction.KYCVASTransaction": self.get_vas_transaction_fields(),
            "transaction.EpinVASTransaction": self.get_epin_vas_fields(),
            "transaction.VirtualAccountVasTransaction": self.get_virtual_account_vas_fields(),
            "fees.BusinessFee": self.get_business_fee_fields(),
            "fees.VenderFee": self.get_vender_fee_fields(),
        }

        if model_name not in model_configs:
            self.stdout.write(
                self.style.ERROR(
                    f'Model {model_name} is not supported. Available: {", ".join(model_configs.keys())}'
                )
            )
            return

        try:
            ExportFieldManager.setup_model_fields(model_name, model_configs[model_name])
            self.stdout.write(
                self.style.SUCCESS(f"✓ {model_name} fields setup complete")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up {model_name}: {str(e)}")
            )

    def get_transaction_fields(self):
        """Get transaction field configuration"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "reference",
                "field_label": "Reference",
                "field_type": "CharField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "merchant_reference",
                "field_label": "Merchant Reference",
                "field_type": "CharField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "status",
                "field_label": "Status",
                "field_type": "CharField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "mode",
                "field_label": "Mode",
                "field_type": "CharField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "txn_class",
                "field_label": "Transaction Class",
                "field_type": "CharField",
                "is_default": True,
                "order": 6,
            },
            {
                "field_name": "type",
                "field_label": "Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "amount",
                "field_label": "Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "charge",
                "field_label": "Charge",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 9,
            },
            {
                "field_name": "revenue",
                "field_label": "Revenue",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 10,
            },
            {
                "field_name": "net_amount",
                "field_label": "Net Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 11,
            },
            {
                "field_name": "old_balance",
                "field_label": "Old Balance",
                "field_type": "DecimalField",
                "is_default": False,
                "order": 12,
            },
            {
                "field_name": "new_balance",
                "field_label": "New Balance",
                "field_type": "DecimalField",
                "is_default": False,
                "order": 13,
            },
            {
                "field_name": "narration",
                "field_label": "Narration",
                "field_type": "TextField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "business.name",
                "field_label": "Business Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "wallet.type",
                "field_label": "Wallet Type",
                "field_type": "CharField",
                "is_default": False,
                "order": 16,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 17,
            },
            {
                "field_name": "updated_at",
                "field_label": "Updated At",
                "field_type": "DateTimeField",
                "is_default": False,
                "order": 18,
            },
        ]

    def get_audit_log_fields(self):
        """Get audit log field configuration"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "email",
                "field_label": "User Email",
                "field_type": "EmailField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "action",
                "field_label": "Action",
                "field_type": "CharField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "description",
                "field_label": "Description",
                "field_type": "TextField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "ip_address",
                "field_label": "IP Address",
                "field_type": "GenericIPAddressField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "user_agent",
                "field_label": "User Agent",
                "field_type": "TextField",
                "is_default": False,
                "order": 6,
            },
            {
                "field_name": "status",
                "field_label": "Status",
                "field_type": "CharField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "resource_type",
                "field_label": "Resource Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "resource_id",
                "field_label": "Resource ID",
                "field_type": "CharField",
                "is_default": False,
                "order": 9,
            },
            {
                "field_name": "old_values",
                "field_label": "Old Values",
                "field_type": "JSONField",
                "is_default": False,
                "is_sensitive": True,
                "order": 10,
            },
            {
                "field_name": "new_values",
                "field_label": "New Values",
                "field_type": "JSONField",
                "is_default": False,
                "is_sensitive": True,
                "order": 11,
            },
            {
                "field_name": "metadata",
                "field_label": "Metadata",
                "field_type": "JSONField",
                "is_default": False,
                "order": 12,
            },
            {
                "field_name": "session_id",
                "field_label": "Session ID",
                "field_type": "CharField",
                "is_default": False,
                "order": 13,
            },
            {
                "field_name": "request_id",
                "field_label": "Request ID",
                "field_type": "CharField",
                "is_default": False,
                "order": 14,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 15,
            },
        ]

    def get_wallet_fields(self):
        """Get wallet field configuration"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "type",
                "field_label": "Wallet Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "balance",
                "field_label": "Balance",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "bank_name",
                "field_label": "Bank Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "account_number",
                "field_label": "Account Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "account_name",
                "field_label": "Account Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 6,
            },
            {
                "field_name": "business.name",
                "field_label": "Business Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "business.owner.email",
                "field_label": "Owner Email",
                "field_type": "EmailField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 9,
            },
            {
                "field_name": "updated_at",
                "field_label": "Updated At",
                "field_type": "DateTimeField",
                "is_default": False,
                "order": 10,
            },
        ]

    def get_vas_transaction_fields(self):
        """Get VAS transaction field configuration (common for all VAS models)"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "reference",
                "field_label": "Reference",
                "field_type": "CharField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "merchant_reference",
                "field_label": "Merchant Reference",
                "field_type": "CharField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "status",
                "field_label": "Status",
                "field_type": "CharField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "mode",
                "field_label": "Mode",
                "field_type": "CharField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "amount",
                "field_label": "Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 6,
            },
            {
                "field_name": "charge",
                "field_label": "Charge",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "net_amount",
                "field_label": "Net Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "narration",
                "field_label": "Narration",
                "field_type": "TextField",
                "is_default": True,
                "order": 9,
            },
            {
                "field_name": "business.name",
                "field_label": "Business Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 10,
            },
            {
                "field_name": "wallet.type",
                "field_label": "Wallet Type",
                "field_type": "CharField",
                "is_default": False,
                "order": 11,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 12,
            },
            {
                "field_name": "updated_at",
                "field_label": "Updated At",
                "field_type": "DateTimeField",
                "is_default": False,
                "order": 13,
            },
        ]

    def get_business_fee_fields(self):
        """Get business fee field configuration (commission rates)"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "product",
                "field_label": "Product",
                "field_type": "CharField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "provider",
                "field_label": "Provider",
                "field_type": "CharField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "fee_type",
                "field_label": "Fee Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "amount",
                "field_label": "Fee Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "cap_amount",
                "field_label": "Cap Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 6,
            },
            {
                "field_name": "business.name",
                "field_label": "Business Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "business.owner.email",
                "field_label": "Owner Email",
                "field_type": "EmailField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 9,
            },
            {
                "field_name": "updated_at",
                "field_label": "Updated At",
                "field_type": "DateTimeField",
                "is_default": False,
                "order": 10,
            },
        ]

    def get_vender_fee_fields(self):
        """Get vender fee field configuration (commission rates)"""
        return [
            {
                "field_name": "id",
                "field_label": "ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 1,
            },
            {
                "field_name": "vender",
                "field_label": "Vender",
                "field_type": "CharField",
                "is_default": True,
                "order": 2,
            },
            {
                "field_name": "product",
                "field_label": "Product",
                "field_type": "CharField",
                "is_default": True,
                "order": 3,
            },
            {
                "field_name": "provider",
                "field_label": "Provider",
                "field_type": "CharField",
                "is_default": True,
                "order": 4,
            },
            {
                "field_name": "fee_type",
                "field_label": "Fee Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 5,
            },
            {
                "field_name": "amount",
                "field_label": "Fee Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 6,
            },
            {
                "field_name": "cap_amount",
                "field_label": "Cap Amount",
                "field_type": "DecimalField",
                "is_default": True,
                "order": 7,
            },
            {
                "field_name": "created_at",
                "field_label": "Created At",
                "field_type": "DateTimeField",
                "is_default": True,
                "order": 8,
            },
            {
                "field_name": "updated_at",
                "field_label": "Updated At",
                "field_type": "DateTimeField",
                "is_default": False,
                "order": 9,
            },
        ]

    def get_airtime_vas_fields(self):
        """Get airtime VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add airtime-specific fields
        airtime_fields = [
            {
                "field_name": "network",
                "field_label": "Network",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "phone_number",
                "field_label": "Phone Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
        ]

        return base_fields + airtime_fields

    def get_data_vas_fields(self):
        """Get data VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add data-specific fields
        data_fields = [
            {
                "field_name": "network",
                "field_label": "Network",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "phone_number",
                "field_label": "Phone Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "data_code",
                "field_label": "Data Code",
                "field_type": "CharField",
                "is_default": True,
                "order": 16,
            },
        ]

        return base_fields + data_fields

    def get_electricity_vas_fields(self):
        """Get electricity VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add electricity-specific fields
        electricity_fields = [
            {
                "field_name": "meter_number",
                "field_label": "Meter Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "biller",
                "field_label": "Biller",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "phone_number",
                "field_label": "Phone Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 16,
            },
            {
                "field_name": "disco",
                "field_label": "DISCO",
                "field_type": "CharField",
                "is_default": True,
                "order": 17,
            },
            {
                "field_name": "disco_type",
                "field_label": "DISCO Type",
                "field_type": "CharField",
                "is_default": True,
                "order": 18,
            },
        ]

        return base_fields + electricity_fields

    def get_cable_tv_vas_fields(self):
        """Get cable TV VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add cable TV-specific fields
        cable_tv_fields = [
            {
                "field_name": "biller",
                "field_label": "Biller",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "iuc_number",
                "field_label": "IUC Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "plan_code",
                "field_label": "Plan Code",
                "field_type": "CharField",
                "is_default": False,
                "order": 16,
            },
        ]

        return base_fields + cable_tv_fields

    def get_betting_vas_fields(self):
        """Get betting VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add betting-specific fields
        betting_fields = [
            {
                "field_name": "biller",
                "field_label": "Biller",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "customer_id",
                "field_label": "Customer ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
        ]

        return base_fields + betting_fields

    def get_epin_vas_fields(self):
        """Get epin VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add epin-specific fields
        epin_fields = [
            {
                "field_name": "network",
                "field_label": "Network",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "quantity",
                "field_label": "Quantity",
                "field_type": "IntegerField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "epin_amount",
                "field_label": "Epin Amount",
                "field_type": "IntegerField",
                "is_default": True,
                "order": 16,
            },
        ]

        return base_fields + epin_fields

    def get_virtual_account_vas_fields(self):
        """Get virtual account VAS transaction field configuration with specific fields"""
        base_fields = self.get_vas_transaction_fields()

        # Add virtual account-specific fields
        va_fields = [
            {
                "field_name": "session_id",
                "field_label": "Session ID",
                "field_type": "CharField",
                "is_default": True,
                "order": 14,
            },
            {
                "field_name": "source_account_number",
                "field_label": "Source Account Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 15,
            },
            {
                "field_name": "source_account_name",
                "field_label": "Source Account Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 16,
            },
            {
                "field_name": "source_bank_name",
                "field_label": "Source Bank Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 17,
            },
            {
                "field_name": "recipient_account_number",
                "field_label": "Recipient Account Number",
                "field_type": "CharField",
                "is_default": True,
                "order": 18,
            },
            {
                "field_name": "recipient_account_name",
                "field_label": "Recipient Account Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 19,
            },
            {
                "field_name": "recipient_bank_name",
                "field_label": "Recipient Bank Name",
                "field_type": "CharField",
                "is_default": True,
                "order": 20,
            },
        ]

        return base_fields + va_fields
