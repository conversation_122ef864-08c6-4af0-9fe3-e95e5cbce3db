from datetime import timedelta

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.utils import timezone
from exports.models import ExportRequest
from exports.serializers import CreateExportRequestSerializer
from exports.services import ExportService

User = get_user_model()


class Command(BaseCommand):
    help = "Create test export requests for testing the export system"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=5,
            help="Number of test export requests to create (default: 5)",
        )
        parser.add_argument(
            "--user-email",
            type=str,
            help="Email of existing user to create exports for (optional)",
        )

    def handle(self, *args, **options):
        self.stdout.write("📤 Creating test export requests...")

        # Get user and business
        user, business = self.get_user_business(options.get("user_email"))

        if not user or not business:
            return

        # Create test export requests
        count = options["count"]
        self.create_test_exports(user, business, count)

        self.stdout.write(
            self.style.SUCCESS(f"\n🎉 Created {count} test export requests!")
        )
        self.stdout.write(f"✓ User: {user.email}")
        self.stdout.write(f"✓ Business: {business.name}")
        self.stdout.write("\nYou can now see these exports in:")
        self.stdout.write("- Swagger: GET /api/v1/exports/")
        self.stdout.write("- Django Admin: /admin/exports/exportrequest/")

    def get_user_business(self, user_email=None):
        """Get user and business for testing"""
        if user_email:
            try:
                user = User.objects.get(email=user_email)
                business = user.business
                self.stdout.write(f"✓ Using user: {user.email}")
                return user, business
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"User with email {user_email} not found")
                )
                return None, None
            except AttributeError:
                self.stdout.write(
                    self.style.ERROR(f"User {user_email} has no associated business")
                )
                return None, None

        # Try to find the test user created by seed_test_data
        try:
            user = User.objects.get(email="<EMAIL>")
            business = user.business
            self.stdout.write(f"✓ Using test user: {user.email}")
            return user, business
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    "No test user found. Please run: python manage.py seed_test_data first"
                )
            )
            return None, None
        except AttributeError:
            self.stdout.write(self.style.ERROR("Test user has no associated business"))
            return None, None

    def create_test_exports(self, user, business, count):
        """Create test export requests with different statuses"""
        export_service = ExportService()

        # Different export configurations to test
        export_configs = [
            {
                "export_type": "transactions",
                "model_name": "transaction.Transaction",
                "fields_to_export": [
                    "id",
                    "reference",
                    "amount",
                    "status",
                    "created_at",
                ],
                "filters": {
                    "date_from": "2024-01-01T00:00:00",
                    "date_to": "2024-12-31T23:59:59",
                },
            },
            {
                "export_type": "transactions",
                "model_name": "transaction.Transaction",
                "fields_to_export": [],  # Test auto-selection
                "filters": {},
            },
            {
                "export_type": "audit_logs",
                "model_name": "audit.AuditLog",
                "fields_to_export": [
                    "id",
                    "email",
                    "action",
                    "description",
                    "created_at",
                ],
                "filters": {"date_from": "2024-06-01T00:00:00"},
            },
            {
                "export_type": "commissions",
                "model_name": "wallet.Wallet",
                "fields_to_export": ["id", "type", "balance", "business.name"],
                "filters": {},
            },
            {
                "export_type": "transactions",
                "model_name": "transaction.Transaction",
                "fields_to_export": [
                    "id",
                    "reference",
                    "merchant_reference",
                    "status",
                    "mode",
                    "amount",
                    "charge",
                    "revenue",
                ],
                "filters": {
                    "date_from": "2024-01-01T00:00:00",
                    "date_to": "2024-06-30T23:59:59",
                },
            },
        ]

        exports_created = 0

        for i in range(count):
            try:
                # Cycle through different configurations
                config = export_configs[i % len(export_configs)]

                # Validate and process fields through serializer first
                serializer = CreateExportRequestSerializer(data=config)
                if not serializer.is_valid():
                    self.stdout.write(
                        self.style.WARNING(
                            f"Invalid config for export {i+1}: {serializer.errors}"
                        )
                    )
                    continue

                validated_data = serializer.validated_data

                # Create export request with validated data
                export_request = export_service.create_export_request(
                    user=user,
                    business=business,
                    model_name=validated_data["model_name"],
                    export_type=validated_data["export_type"],
                    fields_to_export=validated_data[
                        "fields_to_export"
                    ],  # This will be auto-populated if empty
                    filters=validated_data.get("filters", {}),
                )

                # Set different statuses for testing
                if i == 0:
                    # First one completed
                    export_request.status = ExportRequest.Status.COMPLETED
                    export_request.total_records = 25
                    export_request.processed_records = 25
                    export_request.file_size = 1024 * 50  # 50KB
                    export_request.download_url = (
                        "https://example.com/download/test-file.csv"
                    )
                elif i == 1:
                    # Second one processing
                    export_request.status = ExportRequest.Status.PROCESSING
                    export_request.total_records = 100
                    export_request.processed_records = 45
                elif i == 2:
                    # Third one failed
                    export_request.status = ExportRequest.Status.FAILED
                    export_request.error_message = (
                        "Test error: Simulated failure for testing"
                    )
                elif i == 3:
                    # Fourth one expired
                    export_request.status = ExportRequest.Status.EXPIRED
                    export_request.expires_at = timezone.now() - timedelta(days=1)
                # Last ones remain pending

                export_request.save()
                exports_created += 1

                self.stdout.write(
                    f"✓ Created export {i+1}: {export_request.export_type} ({export_request.status})"
                )

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"Failed to create export {i+1}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Successfully created {exports_created} export requests"
            )
        )

        # Show summary
        self.show_export_summary(user, business)

    def show_export_summary(self, user, business):
        """Show summary of created exports"""
        exports = ExportRequest.objects.filter(user=user, business=business)

        self.stdout.write("\n📊 Export Summary:")
        for status in ExportRequest.Status:
            count = exports.filter(status=status.value).count()
            if count > 0:
                self.stdout.write(f"   {status.label}: {count}")

        self.stdout.write(f"\n📋 Total exports: {exports.count()}")

        # Show recent exports
        recent_exports = exports.order_by("-created_at")[:3]
        if recent_exports:
            self.stdout.write("\n🕒 Recent exports:")
            for export in recent_exports:
                self.stdout.write(
                    f'   • {export.export_type} - {export.status} - {export.created_at.strftime("%Y-%m-%d %H:%M")}'
                )
