import logging

from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import ExportRequest
from .serializers import CreateExportRequestSerializer, ExportRequestSerializer
from .services import ExportService
from .tasks import process_export_task

logger = logging.getLogger(__name__)


class ExportViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Simplified ViewSet for data exports - create and list only
    """

    serializer_class = ExportRequestSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["status", "export_type", "created_at"]
    ordering = ["-created_at"]

    # Only allow list, create, and custom actions
    http_method_names = ["get", "post", "head", "options"]

    def get_queryset(self):
        """Filter exports by user's business"""
        user = self.request.user
        if hasattr(user, "business"):
            return ExportRequest.objects.filter(business=user.business)
        return ExportRequest.objects.none()

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return CreateExportRequestSerializer
        return ExportRequestSerializer

    def create(self, request):
        """Create a new export request"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        if not hasattr(user, "business") or not user.business:
            return Response(
                {"error": "User must be associated with a business"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Additional permission check for audit logs
        model_name = serializer.validated_data["model_name"]
        if model_name == "audit.AuditLog" and user != user.business.owner:
            return Response(
                {"error": "Only business owners can export audit logs"},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            # Create export request
            export_service = ExportService()
            export_request = export_service.create_export_request(
                user=user,
                business=user.business,
                model_name=model_name,
                export_type=serializer.validated_data["export_type"],
                fields_to_export=serializer.validated_data["fields_to_export"],
                filters=serializer.validated_data.get("filters", {}),
            )

            # Start background processing
            process_export_task.delay(export_request.id)

            # Return created export request
            response_serializer = ExportRequestSerializer(export_request)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            logger.warning(f"Validation error creating export request: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating export request: {str(e)}")
            return Response(
                {"error": "Failed to create export request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def supported_models(self, request):
        """Get list of supported models for export"""
        supported_models = CreateExportRequestSerializer.SUPPORTED_MODELS
        return Response(
            {
                "supported_models": [
                    {
                        "key": key,
                        "model_name": model_name,
                        "display_name": key.replace("_", " ").title(),
                    }
                    for key, model_name in supported_models.items()
                ]
            }
        )

    @action(detail=False, methods=["delete"])
    def cleanup_expired(self, request):
        """Manually trigger cleanup of expired exports (admin only)"""
        user = request.user
        if not (user.is_staff or getattr(user, "role", "") in ["admin", "super_admin"]):
            return Response(
                {"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN
            )

        try:
            from .tasks import cleanup_expired_exports

            cleanup_expired_exports.delay()
            return Response({"message": "Cleanup task started"})
        except Exception as e:
            logger.error(f"Error starting cleanup task: {str(e)}")
            return Response(
                {"error": "Failed to start cleanup task"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
