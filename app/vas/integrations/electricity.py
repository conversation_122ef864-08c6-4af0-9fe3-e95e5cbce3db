from vas.integrations.base import BaseVASGateClient


class ElectricityVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def validate_meter(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/electricity/validate-meter/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def purchase_electricity(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/electricity/purchase-electricity/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def fetch_billers(self) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/api/v1/electricity/fetch-billers/",
            data={},
        )
        return self._map_response(response, None, status_code)
