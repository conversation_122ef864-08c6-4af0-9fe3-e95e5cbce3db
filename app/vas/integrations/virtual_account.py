from common.dtos import VasGateServiceResponse
from vas.integrations.base import BaseVASGateClient


class VirtualAccountVasClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def create_account(self, payload) -> tuple[int, VasGateServiceResponse]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/virtual-account/create/",
            data=payload,
        )

        return status_code, VasGateServiceResponse(**(response or {}))
