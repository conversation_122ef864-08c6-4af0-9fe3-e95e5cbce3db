import logging

from common.dtos import VasGateServiceResponse
from vas.integrations.base import BaseVASGateClient

logger = logging.getLogger(__name__)


class FundsTransferVASGateClient(BaseVASGateClient):

    def __init__(self):
        super().__init__()

    def name_enquiry(self, payload) -> tuple[int, VasGateServiceResponse]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/transfer/name-enquiry/",
            data=payload,
        )

        return status_code, VasGateServiceResponse(**(response or {}))

    def funds_transfer(self, payload) -> tuple[int, VasGateServiceResponse]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/transfer/funds-transfer/",
            data=payload,
        )

        return status_code, VasGateServiceResponse(**(response or {}))
