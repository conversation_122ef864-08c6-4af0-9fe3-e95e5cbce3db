from vas.integrations.base import BaseVASGateClient


class DataVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def data_lookup(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/data/lookup/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def data_purchase(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/data/purchase/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)
