from vas.integrations.base import BaseVASGateClient


class EpinVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def purchase_epin(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/epins/vend/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)
