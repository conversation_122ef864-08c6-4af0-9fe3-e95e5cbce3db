from vas.integrations.base import BaseVASGateClient


class CableTVVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def fetch_providers(self) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/api/v1/cable-tv/fetch-providers/",
            data={},
        )
        return self._map_response(response, None, status_code)

    def fetch_billers(self, provider: str) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/api/v1/cable-tv/fetch-central-plans/{provider}",
            data={},
        )
        return self._map_response(response, None, status_code)

    def validate_customer_iuc(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/cable-tv/validate/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def purchase_cable_tv_plan(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/cable-tv/subscribe/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)
