from vas.integrations.base import BaseVASGateClient


class BettingVASGateClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def validate_bet_id(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/betting/validate-bet-id/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def fund_bet_account(self, payload) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/betting/fund/",
            data=payload,
        )
        return self._map_response(response, payload.get("reference"), status_code)

    def fetch_billers(self) -> tuple[int, dict]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/api/v1/betting/fetch-billers",
            data={},
        )
        return self._map_response(response, None, status_code)
