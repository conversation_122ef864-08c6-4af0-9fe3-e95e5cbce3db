from dataclasses import dataclass
from typing import Optional, Union

from common.utils import ToDict


@dataclass
class KolomoniWebhookData(ToDict):
    session_id: Optional[str] = None
    amount: Optional[Union[str, float]] = None
    bank_name: Optional[str] = None
    sender_account_number: Optional[str] = None
    sender_account_name: Optional[str] = None
    sender_bank: Optional[str] = None
    sender_bank_code: Optional[str] = None
    narration: Optional[str] = None
    date: Optional[str] = None
    recipient_account_number: Optional[str] = None
    recipient_account_name: Optional[str] = None
    transaction_reference: Optional[str] = None
    response_code: Optional[str] = None
    response_message: Optional[str] = None
    recipient_bank: Optional[str] = None
    recipient_bank_code: Optional[str] = None
