import logging

from business.models import Business
from common.responses import SuccessResponse
from email_validator import EmailNotValidError, validate_email
from rest_framework import serializers
from vas.v3.virtual_account.dtos import KolomoniWebhookData
from vas.v3.virtual_account.enums import VirtualAccountBank
from vas.v3.virtual_account.exceptions import VirtualAccountError
from vas.v3.virtual_account.handlers import (
    KolomoniWebhookHandler,
    VirtualAccountHandler,
)

logger = logging.getLogger(__name__)


class GenerateVirtualAccountSerializer(serializers.Serializer):
    account_name = serializers.CharField(max_length=255)
    email = serializers.EmailField(max_length=255)
    bvn = serializers.CharField(required=False)
    bank = serializers.ChoiceField(
        VirtualAccountBank.choices(), allow_null=True, required=False
    )

    def validate(self, attrs):
        data = super().validate(attrs)
        bvn = attrs.get("bvn")
        bank = attrs.get("bank")

        if len(bvn) != 11:
            raise serializers.ValidationError({"bvn": "Bvn must be 11 digits"})

        if not bank:
            data["bank"] = "wema"

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def create(self, validated_data):
        business: Business = self.context["business"]

        result = VirtualAccountHandler().create_virtual_account(
            business, validated_data
        )

        return SuccessResponse(
            message="Virtual account created successfully.",
            data={"account_details": result},
        ).to_dict()


class KolomoniVirtualAccountWebhookSerializer(serializers.Serializer):
    session_id = serializers.CharField(required=False, allow_null=True)
    amount = serializers.FloatField(required=False, allow_null=True)

    bank_name = serializers.CharField(required=False, allow_null=True)

    sender_account_number = serializers.CharField(required=False, allow_null=True)
    sender_account_name = serializers.CharField(required=False, allow_null=True)
    sender_bank = serializers.CharField(required=False, allow_null=True)
    sender_bank_code = serializers.CharField(required=False, allow_null=True)

    recipient_account_number = serializers.CharField(required=False, allow_null=True)
    recipient_account_name = serializers.CharField(required=False, allow_null=True)

    transaction_reference = serializers.CharField(required=False, allow_null=True)
    narration = serializers.CharField(required=False, allow_null=True)
    date = serializers.CharField(required=False, allow_null=True)

    response_code = serializers.CharField(required=False, allow_null=True)
    response_message = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        data = super().validate(attrs)
        data["recipient_bank"] = "KOLOMONI MFB"
        data["recipient_bank_code"] = "90480"

        return data

    def create(self, validated_data):
        data = KolomoniWebhookData(**validated_data)
        try:
            KolomoniWebhookHandler().handle(data)
        except VirtualAccountError as e:
            validated_data["response_message"] = str(e)
            validated_data["response_code"] = e.status_code

        return validated_data
