from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from pykolofinance.common.schema import header
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework_api_key.permissions import Has<PERSON><PERSON><PERSON><PERSON>
from transaction.enums import TransactionClassEnum

from .serializers import (
    GenerateVirtualAccountSerializer,
    KolomoniVirtualAccountWebhookSerializer,
)


@extend_schema_view(
    post=extend_schema(
        summary="Generate Virtual Account",
        tags=["vas-virtual-account"],
    )
)
@require_activated_vas_product(TransactionClassEnum.VIRTUAL_ACCOUNT.value)
class VirtualAccountView(generics.GenericAPIView):
    serializer_class = GenerateVirtualAccountSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=header,
)
class KolomoniWebhookView(generics.GenericAPIView):
    serializer_class = KolomoniVirtualAccountWebhookSerializer
    permission_classes = [HasAPIKey]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=data.get("response_code", status.HTTP_200_OK))
