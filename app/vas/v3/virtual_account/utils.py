import json

from django_celery_beat.models import IntervalSchedule, PeriodicTask
from transaction.models.virtual_account import VirtualAccountVasTransaction


def register_kolomoni_va_transaction_single_task(va_txn: VirtualAccountVasTransaction):
    schedule, created = IntervalSchedule.objects.get_or_create(
        every=9, period=IntervalSchedule.SECONDS
    )

    task_name = f"Process Kolomoni VA Transaction {va_txn.id}"

    # Register the one-off task
    PeriodicTask.objects.create(
        interval=schedule,
        name=task_name,
        task="vas.v3.virtual_account.tasks.kolomoni_va_transaction_single_tasks",
        args=json.dumps([va_txn.id]),
        one_off=True,
    )
