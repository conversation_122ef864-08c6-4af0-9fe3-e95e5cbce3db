import logging
from typing import Optional

from core.celery import APP
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction as db_transaction
from ledger.enums import LedgerTypeEnum
from ledger.tasks import credit_ledger
from transaction.enums import TransactionStatusEnum
from transaction.models import Transaction
from transaction.models.virtual_account import VirtualAccountVasTransaction
from wallet.models import Wallet

logger = logging.getLogger(__name__)


@APP.task(bind=True, max_retries=5)
def kolomoni_va_transaction_single_tasks(self, transaction_id: int) -> str:
    with db_transaction.atomic():

        va_txn = _get_va_transaction_with_lock(transaction_id)
        if not va_txn:
            return f"Kolomoni: VA Transaction with ID {transaction_id} not found"

        txn = _get_central_transaction(va_txn.reference)
        if not txn:
            return f"Kolomoni: Central Transaction with reference {va_txn.reference} not found"

        if txn_has_been_processed(va_txn):
            return f"Ko<PERSON><PERSON>i: {va_txn.session_id} VA Transaction already processed."

        wallet_result = _process_wallet_credit(va_txn)
        if not wallet_result:
            return f"<PERSON><PERSON><PERSON><PERSON>: Unable to credit wallet {va_txn.wallet.id}"

        txn.status = TransactionStatusEnum.SUCCESSFUL.value
        txn.old_balance = wallet_result["old_balance"]
        txn.new_balance = wallet_result["new_balance"]
        txn.is_wallet_impacted = True
        txn.save(
            update_fields=["status", "old_balance", "new_balance", "is_wallet_impacted"]
        )

        va_txn.status = TransactionStatusEnum.SUCCESSFUL.value
        va_txn.save(update_fields=["status"])

        credit_ledger.delay_on_commit(
            LedgerTypeEnum.VIRTUAL_ACCOUNT_KOLOMONI.value, txn.id
        )

        # @TODO: Send webhook notification

        return f"Kolomoni: Virtual Account Transaction processed successfully: {va_txn.session_id}"


def _get_va_transaction_with_lock(
    transaction_id: int,
) -> Optional[VirtualAccountVasTransaction]:
    return (
        VirtualAccountVasTransaction.objects.select_for_update(skip_locked=True)
        .filter(id=transaction_id)
        .first()
    )


def txn_has_been_processed(va_txn: VirtualAccountVasTransaction) -> bool:
    txn_status = va_txn.status
    print(txn_status)

    return txn_status in [
        TransactionStatusEnum.SUCCESSFUL.value,
        TransactionStatusEnum.FAILED.value,
    ]


def _get_central_transaction(reference: str) -> Optional[Transaction]:
    return (
        Transaction.objects.select_for_update(skip_locked=True)
        .filter(reference=reference)
        .first()
    )


def _process_wallet_credit(va_txn: VirtualAccountVasTransaction):
    try:
        wallet = Wallet.objects.select_for_update().get(id=va_txn.wallet.id)

        # Record balance before credit
        old_balance = wallet.balance

        wallet.credit(va_txn.amount)

        # Record new balance
        new_balance = wallet.balance

        balance_info = {
            "old_balance": old_balance,
            "new_balance": new_balance,
        }

        logger.info(
            f"Wallet {wallet.id} credited successfully. "
            f"Balance changed from {old_balance} to {new_balance}"
        )

        return balance_info

    except ObjectDoesNotExist as e:
        logger.error(f"Error crediting wallet {va_txn.wallet.id}: {str(e)}")

        return None
    except Exception as e:
        logger.error(f"Error crediting wallet {va_txn.wallet.id}: {str(e)}")

        return None
