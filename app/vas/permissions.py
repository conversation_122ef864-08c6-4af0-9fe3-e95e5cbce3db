from business.models import Business, BusinessVASProduct
from rest_framework import permissions


class HasActiveVASProductPermission(permissions.BasePermission):
    message = "This service is not available for your business. Contact support"

    def has_permission(self, request, view):
        product = getattr(view, "required_vas_product", None)
        business: Business = request.user

        if not business or not product:
            return False

        return BusinessVASProduct.objects.filter(
            business=business, product_type=product, is_active=True
        ).exists()
