from business.models import Business
from common.enums import EpinAmount, EpinNetwork
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.epin import EpinVASTransaction
from vas.integrations.epin import EpinVASGateClient


class EpinPurchaseSerializer(serializers.Serializer):
    network = serializers.ChoiceField(choices=EpinNetwork.choices())
    quantity = serializers.IntegerField()
    value = serializers.ChoiceField(choices=EpinAmount.choices())
    reference = serializers.CharField()

    def validate_reference(self, value):
        if EpinVASTransaction.objects.filter(reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def validate_quantity(self, value):
        if value < 100:
            raise serializers.ValidationError("Quantity must be greater than 100")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        network = payload.get("network")
        quantity = payload.get("quantity")
        amount = payload.get("value")
        reference = payload.get("reference")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.EPIN.value,
            type=network,
            narration=f"{network} Epin Purchase",
            reference=reference,
        )
        txn = handler.debit_wallet(params)
        vas_extra_fields = {
            "network": network,
            "quantity": quantity,
            "amount": amount,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.EPIN.value,
            extra_fields=vas_extra_fields,
        )
        client = EpinVASGateClient()
        status_code, response = client.purchase_epin(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
