from business.models import Business
from common.dtos import CoreServiceResponse
from common.enums import CoreServiceResponseStatus, VasGateStatus
from config.models import Bank
from django.db import transaction
from django.db.models import Q
from ledger.enums import LedgerTypeEnum
from ledger.tasks import credit_ledger
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.handlers.base import TransactionHandler
from transaction.handlers.transfer import FundsTransferTransactionHandler
from vas.integrations.transfers import FundsTransferVASGateClient


class NameEnquirySerializer(serializers.Serializer):
    bank_code = serializers.CharField(max_length=6, min_length=3)
    account_number = serializers.CharField(max_length=10, min_length=10)

    def validate(self, attrs):
        data = super().validate(attrs)
        bank_code = attrs.get("bank_code")

        bank = Bank.objects.filter(
            Q(institution_code=bank_code) | Q(bank_code=bank_code)
        ).first()
        if not bank:
            raise serializers.ValidationError(
                {"bank_code": "Invalid bank code selected"}
            )

        # @Todo: select the bank code (institution_code or cbn_code) to use based on the active provider
        data["bank_code"] = bank.institution_code

        return data

    def save(self, **kwargs):
        data = self.validated_data
        _, response = FundsTransferVASGateClient().name_enquiry(
            {
                "bank_code": data["bank_code"],
                "account_number": data["account_number"],
            }
        )

        if response.status == VasGateStatus.Failed.value:
            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message=response.message,
            )

        return CoreServiceResponse(
            success=True,
            status=CoreServiceResponseStatus.Success.value,
            message=response.message,
            data=response.data,
        )


class FundsTransferSerializer(serializers.Serializer):
    reference = serializers.CharField()
    bank_code = serializers.CharField()
    account_number = serializers.CharField()
    account_name = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    narration = serializers.CharField(max_length=50, min_length=3)

    def validate(self, attrs):
        data = super().validate(attrs)
        bank_code = attrs.get("bank_code")

        bank = Bank.objects.filter(
            Q(institution_code=bank_code) | Q(bank_code=bank_code)
        ).first()
        if not bank:
            raise serializers.ValidationError(
                {"bank_code": "Invalid bank code selected"}
            )

        # @Todo: select the bank code (institution_code or cbn_code) to use based on the active provider
        data["bank_code"] = bank.institution_code
        data["bank_name"] = bank.name

        return data

    def save(self, **kwargs):
        data = self.validated_data
        reference = data.get("reference")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        handler = TransactionHandler()
        params = self.__create_base_txn_params(wallet, business, reference, data)

        txn = handler.debit_wallet(params)

        vas_extra_fields = self.__get_extra_fields(data)
        vas_txn = FundsTransferTransactionHandler().create_vas_transaction(
            txn, vas_extra_fields
        )
        credit_ledger.delay(LedgerTypeEnum.TRANSFER.value, txn.id)

        data["reference"] = txn.reference
        _, response = FundsTransferVASGateClient().funds_transfer(
            {
                "bank_code": data["bank_code"],
                "account_number": data["account_number"],
                "amount": float(data["amount"]),
                "reference": data["reference"],
                "narration": data["narration"],
            }
        )

        if response.status == VasGateStatus.Success.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

            return CoreServiceResponse(
                success=True,
                status=CoreServiceResponseStatus.Success.value,
                message="Funds transfer successful.",
                reference=reference,
                data=response.data,
            )

        if response.status == VasGateStatus.Failed.value:
            with transaction.atomic():
                txn.status = TransactionStatusEnum.FAILED.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

                # @TODO: process reversal

            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message=response.message,
                reference=reference,
                data=response.data,
            )

        return CoreServiceResponse(
            success=False,
            status=CoreServiceResponseStatus.Pending.value,
            message="Funds transfer pending.",
            reference=reference,
            data=response.data,
        )

    @staticmethod
    def __create_base_txn_params(wallet, business, reference, payload):
        amount = payload.get("amount")
        bank_name = payload.get("bank_name")
        account_number = payload.get("account_number")
        return CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.TRANSFER.value,
            type=TransactionClassEnum.TRANSFER.value,
            narration=f"Transfer of N{amount} to {account_number} {bank_name}",
            reference=reference,
        )

    @staticmethod
    def __get_extra_fields(payload):
        return {
            "recipient_account_number": payload.get("account_number"),
            "recipient_account_name": payload.get("account_name"),
            "recipient_bank_name": payload.get("bank_name"),
            "recipient_bank_code": payload.get("bank_code"),
        }
