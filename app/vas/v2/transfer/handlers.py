from datetime import timedelta

from config.models import Bank
from django.core.cache import cache
from django.db.models import F


class FundsTransferHandler:

    @staticmethod
    def fetch_bank_list():
        cache_key = "bank::list"
        cache_timeout = timedelta(hours=100)

        cached_banks = cache.get(cache_key)
        if cached_banks:
            banks = cached_banks
        else:
            banks = list(
                Bank.objects.annotate(
                    cbn_code=F("institution_code"), bank_name=F("name")
                ).values("cbn_code", "bank_name")
            )
            cache.set(cache_key, banks, timeout=cache_timeout.total_seconds())

        return banks
