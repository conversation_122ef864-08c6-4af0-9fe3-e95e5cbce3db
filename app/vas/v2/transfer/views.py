from business.models import Business
from common.decorators import (
    enforce_unique_reference,
    require_activated_vas_product,
)
from common.serializers import EmptySerializer
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum
from vas.v2.auth.authentication import BusinessJWTAuthentication
from vas.v2.transfer.handlers import FundsTransferHandler
from vas.v2.transfer.serializers import FundsTransferSerializer, NameEnquirySerializer


@extend_schema_view(
    get=extend_schema(
        summary="Fetch Bank List",
        tags=["vas-transfer"],
    )
)
@require_activated_vas_product(TransactionClassEnum.TRANSFER.value)
class BankListView(generics.GenericAPIView):
    serializer_class = EmptySerializer
    authentication_classes = [BusinessJWTAuthentication]

    def get(self, request, *args, **kwargs):
        banks = FundsTransferHandler().fetch_bank_list()

        return Response({"success": True, "banks": banks}, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Name Enquiry",
        tags=["vas-transfer"],
    )
)
class NameEnquiryView(generics.GenericAPIView):
    serializer_class = NameEnquirySerializer
    authentication_classes = [BusinessJWTAuthentication]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Funds Transfer",
        tags=["vas-transfer"],
    )
)
@require_activated_vas_product(TransactionClassEnum.TRANSFER.value)
class FundsTransferView(generics.GenericAPIView):
    serializer_class = FundsTransferSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        business: Business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data.to_dict(), status=status.HTTP_200_OK)
