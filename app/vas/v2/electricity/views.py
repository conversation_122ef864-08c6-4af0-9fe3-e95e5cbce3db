from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum

from .serializers import (
    ElectricityPurchaseRequestSerializer,
    FetchElectricityBillersSerializer,
    ValidateMeterRequestSerializer,
)


@extend_schema_view(
    post=extend_schema(
        summary="Validate Meter",
        tags=["vas-electricity"],
    )
)
@require_activated_vas_product(TransactionClassEnum.ELECTRICITY.value)
class ValidateMeterView(generics.GenericAPIView):
    serializer_class = ValidateMeterRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Purchase Electricity",
        tags=["vas-electricity"],
    )
)
@require_activated_vas_product(TransactionClassEnum.ELECTRICITY.value)
class ElectricityPurchaseView(generics.GenericAPIView):
    serializer_class = ElectricityPurchaseRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        summary="Fetch Electricity Billers",
        tags=["vas-electricity"],
    )
)
@require_activated_vas_product(TransactionClassEnum.ELECTRICITY.value)
class FetchElectricityBillersView(generics.GenericAPIView):
    serializer_class = FetchElectricityBillersSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
