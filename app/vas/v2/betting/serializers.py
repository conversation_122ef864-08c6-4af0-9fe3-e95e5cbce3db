import logging

from business.models import Business
from common.enums import BetBillerEnum
from common.kgs import generate_uuid7
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.base import Transaction
from vas.integrations.betting import BettingVASGateClient

logger = logging.getLogger(__name__)
BET_ACCOUNT_LOOKUP_VERIFICATION_FEE = 150  # TODO: Get from merchant fee settings


class FetchBettingBillersSerializer(serializers.Serializer):
    def save(self, **kwargs):
        client = BettingVASGateClient()
        status_code, response = client.fetch_billers()
        return response


class ValidateBettingIdRequestSerializer(serializers.Serializer):
    customerId = serializers.CharField()
    type = serializers.ChoiceField(choices=BetBillerEnum.choices())

    def save(self, **kwargs):
        payload = self.validated_data
        customer_id = payload.get("customerId")
        type = payload.get("type")
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=BET_ACCOUNT_LOOKUP_VERIFICATION_FEE,
            txn_class=TransactionClassEnum.BETTING.value,
            type=type,
            narration=f"Betting Account Lookup - {type}",
            reference=generate_uuid7(),
        )
        handler = TransactionHandler()
        txn = handler.debit_wallet(params)
        vas_extra_fields = {"biller": type, "customer_id": customer_id}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.BETTING.value,
            extra_fields=vas_extra_fields,
        )
        client = BettingVASGateClient()
        payload["provider"] = type
        payload["bet_id"] = customer_id
        status_code, response = client.validate_bet_id(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response


class FundBetRequestSerializer(serializers.Serializer):
    reference = serializers.CharField()
    customerId = serializers.CharField()
    type = serializers.ChoiceField(choices=BetBillerEnum.choices())
    name = serializers.CharField()
    amount = serializers.IntegerField()

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        amount = payload.get("amount")
        reference = payload.get("reference")
        customer_id = payload.get("customerId")
        type = payload.get("type")
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.BETTING.value,
            type=type,
            narration=f"Bet Funding - {type}",
            reference=reference,
        )
        txn = handler.debit_wallet(params)
        vas_extra_fields = {"biller": type, "customer_id": customer_id}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.BETTING.value,
            extra_fields=vas_extra_fields,
        )
        client = BettingVASGateClient()
        payload["provider"] = type
        payload["bet_id"] = customer_id
        status_code, response = client.fund_bet_account(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
