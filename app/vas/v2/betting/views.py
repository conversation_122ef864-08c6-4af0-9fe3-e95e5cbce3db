from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum

from .serializers import (
    FetchBettingBillersSerializer,
    FundBetRequestSerializer,
    ValidateBettingIdRequestSerializer,
)


@extend_schema_view(
    post=extend_schema(
        summary="Validate Betting ID",
        tags=["vas-betting"],
    )
)
@require_activated_vas_product(TransactionClassEnum.BETTING.value)
class ValidateBettingAccountView(generics.GenericAPIView):
    serializer_class = ValidateBettingIdRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Fund Betting Account",
        tags=["vas-betting"],
    )
)
@require_activated_vas_product(TransactionClassEnum.BETTING.value)
class FundBettingAccountView(generics.GenericAPIView):
    serializer_class = FundBetRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        summary="Fetch Betting Billers",
        tags=["vas-betting"],
    )
)
@require_activated_vas_product(TransactionClassEnum.BETTING.value)
class FetchBettingBillersView(generics.GenericAPIView):
    serializer_class = FetchBettingBillersSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
