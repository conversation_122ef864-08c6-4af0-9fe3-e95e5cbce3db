from rest_framework import generics, status
from rest_framework.response import Response

from .serializers import BusinessAuthorizationSerializer


class BusinessAuthorizationViewSet(generics.GenericAPIView):
    serializer_class = BusinessAuthorizationSerializer
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            context={"header": self.request.headers.get("Authorization")}
        )
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
