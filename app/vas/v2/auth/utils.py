from datetime import datetime, timedelta, timezone

import jwt
from django.conf import settings


def generate_access_token(business, expires_in=300):
    now = datetime.now(timezone.utc)
    payload = {
        "sub": "access",
        "business_id": business.id,
        "email": business.email,
        "exp": now + timedelta(seconds=expires_in),
        "iat": now,
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")


def generate_refresh_token(business, expires_in=7 * 24 * 60 * 60):
    now = datetime.now(timezone.utc)
    payload = {
        "sub": "refresh",
        "business_id": business.id,
        "exp": now + timedelta(seconds=expires_in),
        "iat": now,
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")


def decode_token(token):
    return jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
