import base64
import logging
from datetime import datetime, timedelta

from business.models import APIConfig
from django.contrib.auth.hashers import check_password
from vas.v2.auth.exceptions import BusinessAuthenticationException
from vas.v2.auth.utils import generate_access_token

logger = logging.getLogger(__name__)


class BasicAuthHandler:
    def authenticate(self, header):
        if not header or not self.__is_valid_basic_auth(header):
            logger.warning(
                "Header not passed or the header passed is not a valid basic auth header. "
            )
            raise BusinessAuthenticationException()

        basic_auth_code = self.extract_basic_auth_code(header)

        decoded = self.extract_decoded_code(basic_auth_code)

        public_key, private_key = decoded.split(":", 1)

        business = self.attempt(public_key, private_key)

        expires_in = 86_400

        return {
            "business_name": business.name,
            "token": {
                "access_token": generate_access_token(business, expires_in),
                "token_type": "Bearer",
                "expires_at": self.get_expiry_time(expires_in),
            },
        }

    @staticmethod
    def __is_valid_basic_auth(header: str):
        return "basic" in header.lower()

    @staticmethod
    def extract_basic_auth_code(header: str) -> str:
        return header.split(" ", 1)[1]

    @staticmethod
    def extract_decoded_code(basic_auth_code: str) -> str:
        try:
            decoded = base64.b64decode(basic_auth_code).decode("utf-8")
        except (base64.binascii.Error, UnicodeDecodeError):
            logger.warning("Invalid base64 encoded basic auth code.")
            raise BusinessAuthenticationException

        return decoded

    @staticmethod
    def attempt(public_key, private_key):
        api_config = APIConfig.objects.filter(public_key=public_key).first()
        if not api_config:
            logger.warning("API config not found using the pub key")
            raise BusinessAuthenticationException

        if not check_password(private_key, api_config.private_key):
            logger.warning("Invalid credentials")
            raise BusinessAuthenticationException

        return api_config.business

    @staticmethod
    def get_expiry_time(seconds):
        expiry_time = datetime.now() + timedelta(seconds=seconds)
        return expiry_time.strftime("%Y-%m-%d %H:%M:%S")
