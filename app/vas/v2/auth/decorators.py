from functools import wraps

from business.enums import OnboardingStage
from rest_framework import status
from rest_framework.response import Response
from vas.v2.auth.authentication import BusinessJWTAuthentication


def business_is_authenticated(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        auth = BusinessJWTAuthentication()

        business, _ = auth.authenticate(request)

        if not business:
            return _unauthorized_response("Authentication error.")

        if business.onboarding_stage != OnboardingStage.Completed.value:
            return _forbidden_response("Merchant Onboarding Incomplete.")

        request.business = business

        return view_func(self, request, *args, **kwargs)

    return _wrapped_view


def _unauthorized_response(message):
    return Response(
        {
            "success": False,
            "status": "failed",
            "message": message,
            "data": None,
        },
        status=status.HTTP_401_UNAUTHORIZED,
    )


def _forbidden_response(message):
    return Response(
        {
            "success": False,
            "status": "failed",
            "message": message,
            "data": None,
        },
        status=status.HTTP_403_FORBIDDEN,
    )


def _error_response(message):
    return Response(
        {
            "success": False,
            "status": "failed",
            "message": message,
            "data": None,
        },
        status=status.HTTP_400_BAD_REQUEST,
    )
