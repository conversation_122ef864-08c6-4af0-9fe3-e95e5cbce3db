from typing import Dict, List, Optional

from common.enums import CableTVBillerEnum
from django.core.cache import cache
from vas.integrations.cable_tv import CableTVVASGateClient

CABLE_PLAN_CACHE_TTL = 43200  # 12 hours
CABLE_TV_PROVIDERS = [
    {
        "id": "1",
        "name": "GOTV",
        "type": "gotv",
        "narration": "Gotv Cable",
        "image": "https://sagecloud.ng/assets/images/billers/capricorn/gotv.webp",
    },
    {
        "id": "2",
        "name": "DSTV",
        "type": "dstv",
        "narration": "Dstv Cable",
        "image": "https://sagecloud.ng/assets/images/billers/capricorn/dstv.webp",
    },
    {
        "id": "3",
        "name": "STARTIMES",
        "type": "startimes",
        "narration": "Startimes Cable",
        "image": "https://sagecloud.ng/assets/images/billers/capricorn/startimes.webp",
    },
]


def get_cable_tv_plan_cache_key(provider: CableTVBillerEnum) -> str:
    return f"cable_plans_{provider.lower()}"


def cache_cable_tv_plans(
    provider: CableTVBillerEnum, plans: List[dict], ttl: int = CABLE_PLAN_CACHE_TTL
) -> None:
    key = get_cable_tv_plan_cache_key(provider)
    cache.set(key, plans, ttl)


def get_cached_cable_tv_plans(provider: CableTVBillerEnum) -> Optional[List[dict]]:
    key = get_cable_tv_plan_cache_key(provider)
    return cache.get(key)


def get_cached_plan_by_code(provider: CableTVBillerEnum, code: str) -> Optional[dict]:
    plans = get_cached_cable_tv_plans(provider)
    if not plans:
        return None
    return next((plan for plan in plans if plan.get("code") == code), None)


def fetch_and_transform_cable_tv_plans(provider: CableTVBillerEnum) -> List[Dict]:
    """
    Fetch raw plans from VASGate client, transform them into the expected format
    And store them in the cache.
    """
    client = CableTVVASGateClient()
    _, response = client.fetch_billers(provider)

    raw_plans = response.get("data", [])
    transformed = []

    for plan in raw_plans:
        price = str(int(float(plan.get("price", "0"))))
        transformed.append(
            {
                "id": plan.get("id"),
                "type": provider.lower(),
                "code": plan.get("code"),
                "name": plan.get("name"),
                "description": plan.get("description", " "),
                "price": price,
                "amount": price,
            }
        )

    response["plans"] = transformed
    cache_cable_tv_plans(provider, transformed)
    response.pop("data")
    return response
