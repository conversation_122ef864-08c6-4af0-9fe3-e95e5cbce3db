import logging

from business.models import Business
from common.enums import CableTVBillerEnum, CableTVBillerIDEnum
from common.kgs import generate_uuid7
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.base import Transaction
from vas.integrations.cable_tv import CableTVVASGateClient

from .utils import (
    fetch_and_transform_cable_tv_plans,
    get_cached_cable_tv_plans,
    get_cached_plan_by_code,
)

logger = logging.getLogger(__name__)
CABLE_TV_FEE = 150  # TODO: Get from merchant fee settings


class FetchCableTVProviderPlansSerializer(serializers.Serializer):
    def save(self, **kwargs):
        provider = self.context.get("provider")
        cached_plans = get_cached_cable_tv_plans(provider)
        if cached_plans:
            return {
                "success": True,
                "status": "success",
                "message": "Plans fetched successfully",
                "plans": cached_plans,
            }
        response = fetch_and_transform_cable_tv_plans(provider)
        return response


class ValidateCableTVIUCNumberSerializer(serializers.Serializer):
    smartCardNo = serializers.CharField()
    biller_id = serializers.ChoiceField(choices=CableTVBillerIDEnum.choices())

    def save(self, **kwargs):
        payload = self.validated_data
        iuc_number = payload.get("smartCardNo")
        biller_id = payload.get("biller_id")
        type = self._map_biller_id_to_name(biller_id)
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=CABLE_TV_FEE,
            txn_class=TransactionClassEnum.CABLE_TV.value,
            type=type,
            narration=f"Cable TV Account Lookup - {type}",
            reference=generate_uuid7(),
        )
        handler = TransactionHandler()
        txn = handler.debit_wallet(params)
        vas_extra_fields = {"biller": type, "iuc_number": iuc_number}
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.CABLE_TV.value,
            extra_fields=vas_extra_fields,
        )
        client = CableTVVASGateClient()
        payload["provider"] = type
        payload["iuc_number"] = iuc_number
        _, response = client.validate_customer_iuc(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response

    def _map_biller_id_to_name(self, biller_id):
        match biller_id:
            case CableTVBillerIDEnum.DSTV.value:
                return CableTVBillerEnum.DSTV.value
            case CableTVBillerIDEnum.GOTV.value:
                return CableTVBillerEnum.GOTV.value
            case CableTVBillerIDEnum.STARTIMES.value:
                return CableTVBillerEnum.STARTIMES.value


class PurchaseCableTVPlanRequestSerializer(serializers.Serializer):
    reference = serializers.CharField()
    type = serializers.CharField()
    smartCardNo = serializers.CharField()
    code = serializers.CharField()

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def validate_type(self, value):
        choices = CableTVBillerEnum.values()
        if value.upper() not in choices:
            raise serializers.ValidationError("Invalid provider")
        return value

    def validate(self, attrs):
        provider = attrs.get("type")
        code = attrs.get("code")
        plan = get_cached_plan_by_code(provider, code)
        if not plan:
            raise serializers.ValidationError({"code": "Invalid or expired plan code"})

        attrs["amount"] = plan["amount"]
        return attrs

    def save(self, **kwargs):
        payload = self.validated_data
        reference = payload.get("reference")
        type = payload.get("type").upper()
        iuc_number = payload.get("smartCardNo")
        plan_code = payload.get("code")
        amount = payload.get("amount")
        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.CABLE_TV.value,
            type=type,
            narration=f"Cable TV Purchase - {type}",
            reference=reference,
        )
        txn = handler.debit_wallet(params)
        vas_extra_fields = {
            "biller": type,
            "iuc_number": iuc_number,
            "plan_code": plan_code,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.CABLE_TV.value,
            extra_fields=vas_extra_fields,
        )
        client = CableTVVASGateClient()
        payload["provider"] = type
        payload["iuc_number"] = iuc_number
        status_code, response = client.purchase_cable_tv_plan(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response
