from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from common.enums import CableTVBillerEnum
from common.serializers import EmptySerializer
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum
from transaction.exceptions import TransactionException

from .serializers import (
    FetchCableTVProviderPlansSerializer,
    PurchaseCableTVPlanRequestSerializer,
    ValidateCableTVIUCNumberSerializer,
)
from .utils import CABLE_TV_PROVIDERS


@extend_schema_view(
    get=extend_schema(
        summary="Fetch Cable TV Providers",
        tags=["vas-cable-tv"],
    )
)
@require_activated_vas_product(TransactionClassEnum.CABLE_TV.value)
class FetchCableTVProvidersView(generics.GenericAPIView):
    serializer_class = EmptySerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        data = {"success": True, "status": "success", "billers": CABLE_TV_PROVIDERS}
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        summary="Fetch Cable TV Plans",
        tags=["vas-cable-tv"],
    )
)
@require_activated_vas_product(TransactionClassEnum.CABLE_TV.value)
class FetchCableTVPProviderPlansView(generics.GenericAPIView):
    serializer_class = FetchCableTVProviderPlansSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        business = request.business
        provider = request.query_params.get("type")
        if not provider:
            raise TransactionException("Cable TV Type is required")
        if provider.upper() not in CableTVBillerEnum.values():
            raise TransactionException("Invalid Cable TV Type")
        serializer = self.get_serializer(
            data=request.data, context={"business": business, "provider": provider}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Validate Customer IUC Number",
        tags=["vas-cable-tv"],
    )
)
@require_activated_vas_product(TransactionClassEnum.CABLE_TV.value)
class ValidateCustomerIUCNumberView(generics.GenericAPIView):
    serializer_class = ValidateCableTVIUCNumberSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Fund Betting Account",
        tags=["vas-cable-tv"],
    )
)
@require_activated_vas_product(TransactionClassEnum.CABLE_TV.value)
class PurchaseCableTVPlanView(generics.GenericAPIView):
    serializer_class = PurchaseCableTVPlanRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
