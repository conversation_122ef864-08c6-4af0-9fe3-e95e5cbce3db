from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum

from .serializers import (
    JambLookupOptionsSerializer,
    JambPurchaseSerializer,
    JambValidateSerializer,
    WaecLookupSerializer,
    WaecPurchaseSerializer,
)


@extend_schema_view(
    get=extend_schema(
        summary="WAEC Lookup",
        tags=["vas-education"],
    )
)
@require_activated_vas_product(TransactionClassEnum.EDUCATION.value)
class WaecLookupView(generics.GenericAPIView):
    serializer_class = WaecLookupSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="WAEC Purchase",
        tags=["vas-education"],
    )
)
@require_activated_vas_product(TransactionClassEnum.EDUCATION.value)
class WaecPurchaseView(generics.GenericAPIView):
    serializer_class = WaecPurchaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        summary="JAMB Options",
        tags=["vas-education"],
    )
)
@require_activated_vas_product(TransactionClassEnum.EDUCATION.value)
class JambOptionsView(generics.GenericAPIView):
    serializer_class = JambLookupOptionsSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def get(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="JAMB Validate",
        tags=["vas-education"],
    )
)
@require_activated_vas_product(TransactionClassEnum.EDUCATION.value)
class JambValidateView(generics.GenericAPIView):
    serializer_class = JambValidateSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="JAMB Purchase",
        tags=["vas-education"],
    )
)
@require_activated_vas_product(TransactionClassEnum.EDUCATION.value)
class JambPurchaseView(generics.GenericAPIView):
    serializer_class = JambPurchaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
