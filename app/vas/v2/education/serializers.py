import logging

from business.models import Business
from common.enums import (
    EducationServiceTypeEnum,
    JambExamTypeEnum,
)
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.base import Transaction
from vas.integrations.education import EducationVASGateClient

logger = logging.getLogger(__name__)


class WaecLookupSerializer(serializers.Serializer):
    def save(self, **kwargs):
        payload = self.validated_data
        client = EducationVASGateClient()
        status_code, response = client.waec_lookup(payload)
        return response


class WaecPurchaseSerializer(serializers.Serializer):
    numberOfPin = serializers.IntegerField(min_value=1, max_value=10)
    amount = serializers.IntegerField(min_value=100)
    reference = serializers.CharField(max_length=100)

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def validate_amount(self, value):
        if value < 100:
            raise serializers.ValidationError("Amount must be greater than 100")
        return value

    def validate_number_of_pins(self, value):
        if value < 1:
            raise serializers.ValidationError("Number of pins must be greater than 1")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        number_of_pins = payload.get("numberOfPin")
        amount = payload.get("amount")
        reference = payload.get("reference")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()
        handler = TransactionHandler()

        params = CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.EDUCATION.value,
            type=EducationServiceTypeEnum.WAEC_RESULT_CHECKER.value,
            narration="Waec Result Checker",
            reference=reference,
        )
        txn = handler.debit_wallet(params)
        vas_extra_fields = {
            "number_of_pins": number_of_pins,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.EDUCATION.value,
            extra_fields=vas_extra_fields,
        )
        client = EducationVASGateClient()
        payload["number_of_pins"] = number_of_pins
        status_code, response = client.waec_purchase(payload)
        response_status = response.get("status")

        handler.update_base_transaction_status(
            txn=txn,
            status=response_status,
        )
        update_fields = {
            "status": response_status,
        }
        handler.update_vas_transaction(txn, vas_txn, update_fields)
        return response


class JambLookupOptionsSerializer(serializers.Serializer):
    def save(self, **kwargs):
        payload = self.validated_data
        client = EducationVASGateClient()
        status_code, response = client.jamb_lookup(payload)
        return response


class JambValidateSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=JambExamTypeEnum.choices())
    profileCode = serializers.CharField(max_length=100)

    def save(self, **kwargs):
        payload = self.validated_data
        exam_type = payload.get("type")
        profile_code = payload.get("profileCode")
        payload = {"exam_type": exam_type, "profile_code": profile_code}
        client = EducationVASGateClient()
        status_code, response = client.jamb_validate(payload)
        return response


class JambPurchaseSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=JambExamTypeEnum.choices())
    profileCode = serializers.CharField(max_length=100)
    reference = serializers.CharField(max_length=100)
    amount = serializers.IntegerField()
    # isBrilliant=serializers.BooleanField()

    def validate_reference(self, value):
        if Transaction.objects.filter(merchant_reference=value).exists():
            raise serializers.ValidationError("Reference already exists")
        return value

    def validate_amount(self, value):
        if value < 1:
            raise serializers.ValidationError("Amount must be greater than 1")
        return value

    def save(self, **kwargs):
        payload = self.validated_data
        examm_type = payload.get("type")
        profile_code = payload.get("profileCode")
        reference = payload.get("reference")
        amount = payload.get("amount")
        # is_brilliant = payload.get("isBrilliant")
        payload = {
            "exam_type": examm_type,
            "profile_code": profile_code,
            "reference": reference,
            "amount": amount,
        }
        client = EducationVASGateClient()
        status_code, response = client.jamb_purchase(payload)
        return response
