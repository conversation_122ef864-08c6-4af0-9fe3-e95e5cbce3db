# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "email",
                    models.EmailField(
                        db_index=True,
                        help_text="Email of the user who performed the action",
                        max_length=254,
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("LOGIN", "Login"),
                            ("LOGOUT", "Logout"),
                            ("REGISTER", "Registration"),
                            ("PASSWORD_CHANGE", "Password Change"),
                            ("PASSWORD_RESET", "Password Reset"),
                            ("PROFILE_UPDATE", "Profile Update"),
                            ("TRANSACTION_CREATE", "Transaction Created"),
                            ("TRANSACTION_UPDATE", "Transaction Updated"),
                            ("TRANSACTION_REVERSE", "Transaction Reversed"),
                            ("WALLET_CREATE", "Wallet Created"),
                            ("WALLET_UPDATE", "Wallet Updated"),
                            ("WALLET_FUNDING", "Wallet Funding"),
                            ("BUSINESS_CREATE", "Business Created"),
                            ("BUSINESS_UPDATE", "Business Updated"),
                            ("BUSINESS_ONBOARD", "Merchant Onboarded"),
                            ("PIN_CHANGE", "PIN Change"),
                            ("PIN_VERIFY", "PIN Verification"),
                            ("OTP_REQUEST", "OTP Request"),
                            ("OTP_VERIFY", "OTP Verification"),
                            ("TWO_FA_SETUP", "2FA Setup"),
                            ("TWO_FA_DISABLE", "2FA Disable"),
                            ("GOOGLE_AUTH", "Google Authentication"),
                            ("API_ACCESS", "API Access"),
                            ("API_KEY_GENERATE", "API Key Generated"),
                            ("API_KEY_REVOKE", "API Key Revoked"),
                            ("PERMISSION_CHANGE", "Permission Change"),
                            ("ROLE_UPDATE", "User Role Updated"),
                            ("ACCOUNT_DISABLE", "Account Disable"),
                            ("ACCOUNT_ENABLE", "Account Enable"),
                            ("DATA_EXPORT", "Data Export"),
                            ("DATA_IMPORT", "Data Import"),
                            ("SYSTEM_CONFIG", "System Configuration"),
                            ("DISPUTE_CREATE", "Transaction Dispute Raised"),
                            ("DISPUTE_RESOLVE", "Dispute Resolved"),
                            ("PROVIDER_ADD", "New Provider Added"),
                            ("PROVIDER_SWITCH", "Provider Switched"),
                            ("FEE_STRUCTURE_EDIT", "Fee Structure Edited"),
                            ("AUDIT_LOG_ACCESS", "Audit Log Accessed"),
                            ("TEAM_MEMBER_INVITE", "Team Member Invited"),
                            ("ADMIN_LOGIN", "Admin Login"),
                            ("ADMIN_LOGOUT", "Admin Logout"),
                            ("OTHER", "Other"),
                        ],
                        db_index=True,
                        help_text="Type of action performed",
                        max_length=50,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of the action"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        db_index=True,
                        help_text="IP address from which the action was performed",
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True,
                        help_text="User agent string from the request",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("SUCCESS", "Success"),
                            ("FAILED", "Failed"),
                            ("PENDING", "Pending"),
                        ],
                        db_index=True,
                        default="SUCCESS",
                        help_text="Status of the action",
                        max_length=20,
                    ),
                ),
                (
                    "resource_type",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Type of resource affected (e.g., User, Transaction, Wallet)",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "resource_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="ID of the resource affected",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "old_values",
                    models.JSONField(
                        blank=True,
                        help_text="Previous values before the change (for updates)",
                        null=True,
                    ),
                ),
                (
                    "new_values",
                    models.JSONField(
                        blank=True,
                        help_text="New values after the change (for updates)",
                        null=True,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        help_text="Additional metadata about the action",
                        null=True,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Session ID if available",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "request_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Unique request ID for tracing",
                        max_length=100,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Log",
                "verbose_name_plural": "Audit Logs",
                "ordering": ["-created_at"],
            },
        ),
    ]
