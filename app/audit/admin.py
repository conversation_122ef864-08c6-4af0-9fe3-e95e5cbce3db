from django.contrib import admin
from django.utils.html import format_html

from .models import AuditLog


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for AuditLog model
    """

    list_display = [
        "id",
        "email",
        "user_fullname",
        "action_display",
        "status_display",
        "ip_address",
        "resource_info",
        "formatted_timestamp",
    ]

    list_filter = [
        "action",
        "status",
        "resource_type",
        "created_at",
    ]

    search_fields = [
        "email",
        "user__firstname",
        "user__lastname",
        "description",
        "ip_address",
        "resource_id",
    ]

    readonly_fields = [
        "id",
        "user",
        "email",
        "action",
        "description",
        "ip_address",
        "user_agent",
        "status",
        "resource_type",
        "resource_id",
        "old_values",
        "new_values",
        "metadata",
        "session_id",
        "request_id",
        "created_at",
        "updated_at",
        "formatted_timestamp",
    ]

    fieldsets = (
        ("User Information", {"fields": ("user", "email")}),
        ("Action Details", {"fields": ("action", "description", "status")}),
        (
            "Request Information",
            {"fields": ("ip_address", "user_agent", "session_id", "request_id")},
        ),
        ("Resource Information", {"fields": ("resource_type", "resource_id")}),
        (
            "Data Changes",
            {
                "fields": ("old_values", "new_values", "metadata"),
                "classes": ("collapse",),
            },
        ),
        ("Timestamps", {"fields": ("created_at", "updated_at", "formatted_timestamp")}),
    )

    ordering = ["-created_at"]

    def has_add_permission(self, request):
        """Disable adding audit logs through admin"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing audit logs through admin"""
        return False

    def has_delete_permission(self, request, obj=None):
        """Disable deleting audit logs through admin"""
        return False

    def user_fullname(self, obj):
        """Display user's full name"""
        if obj.user:
            return obj.user.fullname
        return "N/A"

    user_fullname.short_description = "User Name"

    def action_display(self, obj):
        """Display action with color coding"""
        colors = {
            "LOGIN": "green",
            "LOGOUT": "blue",
            "REGISTER": "purple",
            "PASSWORD_CHANGE": "orange",
            "FAILED": "red",
        }
        color = colors.get(obj.action, "black")
        return format_html(
            '<span style="color: {};">{}</span>', color, obj.get_action_display()
        )

    action_display.short_description = "Action"

    def status_display(self, obj):
        """Display status with color coding"""
        colors = {
            "SUCCESS": "green",
            "FAILED": "red",
            "PENDING": "orange",
        }
        color = colors.get(obj.status, "black")
        return format_html(
            '<span style="color: {};">{}</span>', color, obj.get_status_display()
        )

    status_display.short_description = "Status"

    def resource_info(self, obj):
        """Display resource information"""
        if obj.resource_type and obj.resource_id:
            return f"{obj.resource_type}: {obj.resource_id}"
        return "N/A"

    resource_info.short_description = "Resource"
