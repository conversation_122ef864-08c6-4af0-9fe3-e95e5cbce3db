"""
Django management command to clear audit log data.

This command removes audit log entries based on specified criteria.
"""

from datetime import timed<PERSON><PERSON>

from audit.models import AuditLog
from django.core.management.base import BaseCommand
from django.utils import timezone


class Command(BaseCommand):
    help = "Clear audit log data based on specified criteria"

    def add_arguments(self, parser):
        parser.add_argument("--all", action="store_true", help="Clear all audit logs")
        parser.add_argument(
            "--older-than", type=int, help="Clear logs older than specified days"
        )
        parser.add_argument(
            "--status",
            choices=["SUCCESS", "FAILED", "PENDING"],
            help="Clear logs with specific status only",
        )
        parser.add_argument("--action", help="Clear logs with specific action only")
        parser.add_argument(
            "--confirm", action="store_true", help="Confirm deletion without prompting"
        )

    def handle(self, *args, **options):
        clear_all = options["all"]
        older_than = options["older_than"]
        status_filter = options["status"]
        action_filter = options["action"]
        confirm = options["confirm"]

        # Build queryset based on filters
        queryset = AuditLog.objects.all()

        if older_than:
            cutoff_date = timezone.now() - timedelta(days=older_than)
            queryset = queryset.filter(created_at__lt=cutoff_date)

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if action_filter:
            queryset = queryset.filter(action=action_filter)

        # Count logs to be deleted
        count = queryset.count()

        if count == 0:
            self.stdout.write(
                self.style.WARNING("No audit logs found matching the criteria.")
            )
            return

        # Show what will be deleted
        self.stdout.write(f"📊 Found {count} audit logs matching criteria:")

        if clear_all:
            self.stdout.write("   → All audit logs")
        if older_than:
            self.stdout.write(f"   → Older than {older_than} days")
        if status_filter:
            self.stdout.write(f"   → Status: {status_filter}")
        if action_filter:
            self.stdout.write(f"   → Action: {action_filter}")

        # Confirm deletion
        if not confirm:
            response = input(
                f"\n  Are you sure you want to delete {count} audit logs? (yes/no): "
            )
            if response.lower() not in ["yes", "y"]:
                self.stdout.write(self.style.WARNING("Operation cancelled."))
                return

        # Perform deletion
        self.stdout.write("🗑️  Deleting audit logs...")
        deleted_count = queryset.delete()[0]

        self.stdout.write(
            self.style.SUCCESS(f" Successfully deleted {deleted_count} audit logs!")
        )
