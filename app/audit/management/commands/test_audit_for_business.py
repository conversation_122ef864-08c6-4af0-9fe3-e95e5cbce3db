"""
Management command to test audit logging for a specific business owner account
"""

from audit.models import AuditLog
from audit.utils import (
    log_api_key_generation,
    log_login_attempt,
    log_logout,
    log_password_change,
    log_profile_update,
    log_team_member_invitation,
)
from audit.views import AuditLogViewSet, BusinessOwnerAuditLogViewSet
from business.models import Business
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.test import RequestFactory

User = get_user_model()


class Command(BaseCommand):
    help = "Test audit logging system for a specific business owner account"

    def add_arguments(self, parser):
        parser.add_argument(
            "--email",
            type=str,
            default="<EMAIL>",
            help="Business owner email to test (default: <EMAIL>)",
        )
        parser.add_argument(
            "--create-logs",
            action="store_true",
            help="Create sample audit logs for testing",
        )
        parser.add_argument(
            "--show-access",
            action="store_true",
            help="Show what logs the business owner can access",
        )

    def handle(self, *args, **options):
        email = options["email"]
        create_logs = options["create_logs"]
        show_access = options["show_access"]

        self.stdout.write(self.style.SUCCESS(f" Testing Audit System for: {email}"))
        self.stdout.write("=" * 60)

        user = self.get_or_create_business_owner(email)

        if create_logs:
            self.create_sample_audit_logs(user)

        if show_access:
            self.test_business_access(user)

        self.show_user_audit_logs(user)

        self.stdout.write(self.style.SUCCESS("\nAudit system test completed!"))

    def get_or_create_business_owner(self, email):
        """Get or create business owner and their business"""
        self.stdout.write(f"\n🔍 Looking for business owner: {email}")

        try:
            user = User.objects.get(email=email)
            self.stdout.write(f"Found existing user: {user.fullname}")

            try:
                if hasattr(user, "business") and user.business:
                    self.stdout.write(f"✓ User has business: {user.business.name}")
                else:
                    self.stdout.write(
                        "User exists but has no business - will create audit logs for user only"
                    )
            except Exception as e:
                self.stdout.write(f" Could not check business relationship: {str(e)}")
                self.stdout.write("   Will proceed with user-only audit logging")

        except User.DoesNotExist:
            self.stdout.write(" User not found - creating business owner account...")

            name_part = email.split("@")[0]
            firstname = name_part.capitalize()

            user = User.objects.create(
                email=email,
                firstname=firstname,
                lastname="Business Owner",
                role="Business_Owner",
                is_active=True,
                verified=True,
            )

            business = Business.objects.create(
                owner=user, name=f"{firstname}'s Business", email=email, status="Active"
            )

            self.stdout.write(f"Created user: {user.fullname}")
            self.stdout.write(f"Created business: {business.name}")

        return user

    def create_sample_audit_logs(self, user):
        """Create sample audit logs for the business owner"""
        self.stdout.write(f"\n Creating sample audit logs for {user.email}...")

        factory = RequestFactory()

        # Create mock request
        request = factory.post("/api/test/")
        request.user = user
        request.META["REMOTE_ADDR"] = "192.168.1.100"
        request.META["HTTP_USER_AGENT"] = "Test Browser for Business Owner"

        log_login_attempt(request, user=user, success=True)
        self.stdout.write("Login event logged")

        log_profile_update(
            request=request,
            user=user,
            old_data={"firstname": "Old", "lastname": "Name"},
            new_data={"firstname": user.firstname, "lastname": user.lastname},
        )
        self.stdout.write("Profile update logged")

        try:
            if hasattr(user, "business") and user.business:
                log_api_key_generation(
                    request=request,
                    user=user,
                    business=user.business,
                    key_type="private",
                )
                self.stdout.write("API key generation logged")
            else:
                self.stdout.write(
                    "Skipped API key generation (no business relationship)"
                )
        except Exception as e:
            self.stdout.write(f"Could not log API key generation: {str(e)}")

        # 4. Password change
        log_password_change(request, user=user, success=True)
        self.stdout.write("  ✓ Password change logged")

        try:
            if hasattr(user, "business") and user.business:
                log_team_member_invitation(
                    request=request,
                    user=user,
                    invited_email="<EMAIL>",
                    role="Merchant_Admin",
                    business=user.business,
                )
                self.stdout.write("Team member invitation logged")
            else:
                self.stdout.write(
                    "Skipped team member invitation (no business relationship)"
                )
        except Exception as e:
            self.stdout.write(f"Could not log team member invitation: {str(e)}")

        log_logout(request=request, user=user)
        self.stdout.write("  ✓ Logout event logged")

        self.stdout.write(f"Created 6 sample audit logs for {user.email}")

    def test_business_access(self, user):
        """Test what audit logs the business owner can access"""
        self.stdout.write(f"\n Testing access controls for {user.email}...")

        factory = RequestFactory()
        request = factory.get("/api/v1/audit/my-logs/")
        request.user = user

        viewset = BusinessOwnerAuditLogViewSet()
        viewset.request = request
        business_queryset = viewset.get_queryset()

        business_logs_count = business_queryset.count()
        self.stdout.write(f"Business owner can see: {business_logs_count} logs")
        admin_request = factory.get("/api/v1/audit/logs/")
        admin_request.user = user

        admin_viewset = AuditLogViewSet()
        admin_viewset.request = admin_request
        admin_queryset = admin_viewset.get_queryset()

        admin_logs_count = admin_queryset.count()
        self.stdout.write(f"Via admin endpoint can see: {admin_logs_count} logs")

        try:
            if hasattr(user, "business") and user.business:
                business_specific_logs = business_queryset.filter(
                    metadata__business_id=str(user.business.id)
                ).count()
                self.stdout.write(
                    f"  🏢 Business-specific logs: {business_specific_logs}"
                )
                other_business_logs = (
                    business_queryset.exclude(user=user)
                    .exclude(metadata__business_id=str(user.business.id))
                    .count()
                )

                if other_business_logs == 0:
                    self.stdout.write(" Business isolation working correctly")
                else:
                    self.stdout.write(
                        f"  Found {other_business_logs} logs from other sources"
                    )
            else:
                self.stdout.write(" No business relationship - showing user-only logs")
        except Exception as e:
            self.stdout.write(f"Could not check business context: {str(e)}")

    def show_user_audit_logs(self, user):
        """Show current audit logs for the user"""
        self.stdout.write(f"\n📋 Current Audit Logs for {user.email}:")
        self.stdout.write("-" * 60)

        user_logs = AuditLog.objects.filter(user=user).order_by("-created_at")[:10]

        if not user_logs.exists():
            self.stdout.write("  📭 No audit logs found for this user")
            return

        for log in user_logs:
            timestamp = log.created_at.strftime("%Y-%m-%d %H:%M:%S")
            action_display = log.get_action_display()
            status_color = (
                self.style.SUCCESS if log.status == "SUCCESS" else self.style.ERROR
            )

            self.stdout.write(
                f"{timestamp} | "
                f"{status_color(log.status.ljust(7))} | "
                f"{action_display.ljust(25)} | "
                f'{log.description[:50]}{"..." if len(log.description) > 50 else ""}'
            )

        total_logs = AuditLog.objects.filter(user=user).count()
        successful_logs = AuditLog.objects.filter(user=user, status="SUCCESS").count()
        failed_logs = AuditLog.objects.filter(user=user, status="FAILED").count()

        self.stdout.write(f"Summary for {user.email}:")
        self.stdout.write(f"  • Total logs: {total_logs}")
        self.stdout.write(f"  • Successful: {successful_logs}")
        self.stdout.write(f"  • Failed: {failed_logs}")

        # Show business context if available
        if hasattr(user, "business") and user.business:
            business_logs = AuditLog.objects.filter(
                metadata__business_id=str(user.business.id)
            ).count()
            self.stdout.write(f"  • Business-related logs: {business_logs}")
            self.stdout.write(f"  • Business: {user.business.name}")

    def show_api_usage_examples(self, user):
        """Show API usage examples for the business owner"""
        self.stdout.write(f"\n API Usage Examples for {user.email}:")
        self.stdout.write("-" * 60)

        self.stdout.write("Business Owner Audit Endpoints:")
        self.stdout.write("  GET /api/v1/audit/my-logs/")
        self.stdout.write("  GET /api/v1/audit/my-logs/?action=LOGIN")
        self.stdout.write("  GET /api/v1/audit/my-logs/?status=SUCCESS")
        self.stdout.write("  GET /api/v1/audit/my-logs/business_activity/")
        self.stdout.write("  GET /api/v1/audit/my-logs/security_activity/")

        if hasattr(user, "business") and user.business:
            self.stdout.write(
                f"\nBusiness Context: {user.business.name} (ID: {user.business.id})"
            )
            self.stdout.write("All logs will be filtered to this business only.")
