from django.db import models
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response

from .filters import AuditLogFilter
from .models import AuditLog
from .serializers import AuditLogListSerializer, AuditLogSerializer


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs

    Provides endpoints for:
    - Listing audit logs with filtering and search
    - Retrieving individual audit log details
    - Getting audit log statistics
    """

    queryset = AuditLog.objects.select_related("user").all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = ["email", "description", "ip_address"]
    ordering_fields = ["created_at", "action", "status", "email"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == "list":
            return AuditLogListSerializer
        return AuditLogSerializer

    def get_queryset(self):
        """
        Filter queryset based on user permissions

        - Admins can see all logs from all businesses
        - Business users can only see logs from their business
        - Regular users can only see their own logs
        """
        queryset = super().get_queryset()
        user = self.request.user

        # Admin roles can see all logs from all businesses
        admin_roles = ["Admin", "Super_Admin", "System_Admin"]
        if user.is_staff or user.role in admin_roles:
            return queryset

        # Business owners and team members can see logs from their business only
        if hasattr(user, "business") and user.business:
            # Get all users from the same business
            business_users = user.business.owner.id
            business_team_members = []

            try:
                from dispute.models import BusinessMember

                team_members = BusinessMember.objects.filter(
                    business=user.business, is_active=True
                ).values_list("user_id", flat=True)
                business_team_members = list(team_members)
            except BusinessMember.DoesNotExist:
                return queryset.filter(user=user)

            business_user_ids = [business_users] + business_team_members

            # Filter logs to business-related users and business-related actions
            return queryset.filter(
                models.Q(user_id__in=business_user_ids)
                | models.Q(metadata__business_id=str(user.business.id))
            )

        return queryset.filter(user=user)

    # TODO: Implement export functionality(WHAT ARE WE EXPORTING AND IN WHAT FORMAT)
    # @action(detail=False, methods=['get'])
    # def export(self, request):
    #     """
    #     Export audit logs (for admin users only)
    #     """
    #     user = request.user
    #     if not (user.is_staff or user.role in ['admin', 'super_admin']):
    #         return Response(
    #             {'error': 'Permission denied'},
    #             status=status.HTTP_403_FORBIDDEN
    #         )

    #     return Response({
    #         'message': 'Export functionality not implemented yet',
    #         'note': 'This endpoint would generate downloadable audit log reports'
    #     })

    def list(self, request, *args, **kwargs):
        """
        Override list to add custom response format
        """
        response = super().list(request, *args, **kwargs)

        # Add metadata to response
        response.data = {
            "success": True,
            "message": "Audit logs retrieved successfully",
            "data": response.data,
        }

        return response

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to add custom response format
        """
        response = super().retrieve(request, *args, **kwargs)

        response.data = {
            "success": True,
            "message": "Audit log retrieved successfully",
            "data": response.data,
        }

        return response


class BusinessOwnerAuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for business owners to view their own audit logs only

    Provides endpoints for:
    - Listing their own audit logs with filtering and search
    - Retrieving individual audit log details (their own only)
    - Getting their own audit log statistics
    """

    queryset = AuditLog.objects.select_related("user").all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = [
        "description",
        "ip_address",
    ]
    ordering_fields = ["created_at", "action", "status"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == "list":
            return AuditLogListSerializer
        elif self.action == "stats":
            # return AuditLogStatsSerializer
            return AuditLogSerializer

    def get_queryset(self):
        """
        Filter to business-related logs only

        Business owners and team members can see:
        1. Their own logs
        2. Logs from other team members in the same business
        3. Business-related actions (even if performed by system/admin on their business)
        """
        user = self.request.user
        queryset = super().get_queryset()

        if not hasattr(user, "business") or not user.business:
            return queryset.filter(user=user)

        # Get all users from the same business
        business_users = [user.business.owner.id]

        # Get team members if business has team members
        try:
            from dispute.models import BusinessMember

            team_members = BusinessMember.objects.filter(
                business=user.business, is_active=True
            ).values_list("user_id", flat=True)
            business_users.extend(list(team_members))
        except BusinessMember.DoesNotExist:
            return queryset.filter(user=user)

        # Filter logs to:
        # 1. Users from the same business
        # 2. Actions that have business context matching their business
        return queryset.filter(
            models.Q(user_id__in=business_users)
            | models.Q(metadata__business_id=str(user.business.id))
        )

    # TODO: delete later or leave if we want business owners to see their stat
    # @action(detail=False, methods=['get'])
    # def stats(self, request):
    #     """
    #     Get audit log statistics for current user only
    #     """
    #     queryset = self.get_queryset()

    #     # Date range filter (default to last 30 days)
    #     days = int(request.query_params.get('days', 30))
    #     start_date = timezone.now() - timedelta(days=days)
    #     queryset = queryset.filter(created_at__gte=start_date)

    #     # Calculate statistics
    #     total_logs = queryset.count()
    #     successful_actions = queryset.filter(status=AuditLog.SUCCESS).count()
    #     failed_actions = queryset.filter(status=AuditLog.FAILED).count()
    #     unique_ips = queryset.values('ip_address').distinct().count()

    #     # Most common actions
    #     most_common_actions = list(
    #         queryset.values('action')
    #         .annotate(count=Count('action'))
    #         .order_by('-count')[:10]
    #     )

    #     # Format action names
    #     for item in most_common_actions:
    #         action_choice = next(
    #             (choice for choice in AuditLog.ACTION_CHOICES if choice[0] == item['action']),
    #             None
    #         )
    #         item['action_display'] = action_choice[1] if action_choice else item['action']

    #     # Recent activity (last 10 logs)
    #     recent_activity = queryset.order_by('-created_at')[:10]

    #     stats_data = {
    #         'total_logs': total_logs,
    #         'successful_actions': successful_actions,
    #         'failed_actions': failed_actions,
    #         'unique_ips': unique_ips,
    #         'most_common_actions': most_common_actions,
    #         'recent_activity': AuditLogListSerializer(recent_activity, many=True).data
    #     }

    #     serializer = self.get_serializer(stats_data)
    #     return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def business_activity(self, request):
        """
        Get business-related activity for current user
        """
        business_actions = [
            AuditLog.BUSINESS_CREATE,
            AuditLog.BUSINESS_UPDATE,
            AuditLog.TRANSACTION_CREATE,
            AuditLog.TRANSACTION_UPDATE,
            AuditLog.WALLET_CREATE,
            AuditLog.WALLET_UPDATE,
        ]

        queryset = self.get_queryset().filter(action__in=business_actions)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = AuditLogListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = AuditLogListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def security_activity(self, request):
        """
        Get security-related activity for current user
        """
        security_actions = [
            AuditLog.LOGIN,
            AuditLog.LOGOUT,
            AuditLog.PASSWORD_CHANGE,
            AuditLog.PIN_CHANGE,
            AuditLog.PIN_VERIFY,
            AuditLog.TWO_FA_SETUP,
            AuditLog.TWO_FA_DISABLE,
            AuditLog.OTP_REQUEST,
            AuditLog.OTP_VERIFY,
        ]

        queryset = self.get_queryset().filter(action__in=security_actions)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = AuditLogListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = AuditLogListSerializer(queryset, many=True)
        return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        """
        Override list to add custom response format
        """
        response = super().list(request, *args, **kwargs)

        # Add metadata to response
        response.data = {
            "success": True,
            "message": "Your audit logs retrieved successfully",
            "data": response.data,
        }

        return response

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to add custom response format
        """
        response = super().retrieve(request, *args, **kwargs)

        response.data = {
            "success": True,
            "message": "Audit log retrieved successfully",
            "data": response.data,
        }

        return response
