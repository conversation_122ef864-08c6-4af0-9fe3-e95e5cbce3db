from rest_framework import serializers

from .models import AuditLog


class AuditLogSerializer(serializers.ModelSerializer):
    """
    Serializer for AuditLog model
    """

    user_fullname = serializers.CharField(source="user.fullname", read_only=True)
    action_display = serializers.Char<PERSON>ield(source="get_action_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    formatted_timestamp = serializers.CharField(read_only=True)

    class Meta:
        model = AuditLog
        fields = [
            "id",
            "email",
            "user_fullname",
            "action",
            "action_display",
            "description",
            "ip_address",
            "user_agent",
            "status",
            "status_display",
            "resource_type",
            "resource_id",
            "old_values",
            "new_values",
            "metadata",
            "session_id",
            "request_id",
            "created_at",
            "formatted_timestamp",
        ]
        read_only_fields = fields  # All fields are read-only for audit logs


class AuditLogListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing audit logs
    """

    user_fullname = serializers.Char<PERSON>ield(source="user.fullname", read_only=True)
    action_display = serializers.CharField(source="get_action_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    formatted_timestamp = serializers.CharField(read_only=True)

    class Meta:
        model = AuditLog
        fields = [
            "id",
            "email",
            "user_fullname",
            "action",
            "action_display",
            "description",
            "ip_address",
            "status",
            "status_display",
            "resource_type",
            "resource_id",
            "created_at",
            "formatted_timestamp",
        ]
        read_only_fields = fields
