#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced dispute timeline feature.

This script shows how the dispute list and detail APIs now provide
enhanced timeline information based on dispute status.

Usage:
    python test_timeline_feature.py

Note: This is a demonstration script. In a real environment, you would
use proper API testing tools or the Django test framework.
"""

import json


# Mock data to simulate API responses
def create_mock_dispute_list():
    """Create mock dispute list response showing the new message field."""
    return {
        "success": True,
        "message": "Disputes retrieved successfully",
        "data": [
            {
                "id": 1,
                "transaction_reference": "TXN_TESTUSER_OWNER1_0001",
                "vas_service": "AIRTIME",
                "vas_service_display": "Airtime",
                "amount": "1500.00",
                "status": "PENDING",
                "status_display": "Pending",
                "merchant_name": "Test Business 1",
                "message": "Transaction failed but amount was debited from my account.\
                      Please investigate and refund.",
                "created_by_name": "Business Owner1",
                "business_name": "Test Business 1",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
            },
            {
                "id": 2,
                "transaction_reference": "TXN_TESTUSER_OWNER1_0002",
                "vas_service": "ELECTRICITY",
                "vas_service_display": "Electricity",
                "amount": "2500.00",
                "status": "IN_REVIEW",
                "status_display": "In Review",
                "merchant_name": "Test Business 1",
                "message": "I was charged twice for the same transaction. The second charge should be reversed.",
                "created_by_name": "Business Owner1",
                "business_name": "Test Business 1",
                "created_at": "2024-01-14T14:20:00Z",
                "updated_at": "2024-01-15T09:15:00Z",
            },
            {
                "id": 3,
                "transaction_reference": "TXN_TESTUSER_OWNER1_0003",
                "vas_service": "DATA",
                "vas_service_display": "Data",
                "amount": "1000.00",
                "status": "RESOLVED",
                "status_display": "Resolved",
                "merchant_name": "Test Business 1",
                "message": "The transaction was successful but the service was not delivered.\
                    Need immediate resolution.",
                "created_by_name": "Business Owner1",
                "business_name": "Test Business 1",
                "created_at": "2024-01-13T08:45:00Z",
                "updated_at": "2024-01-16T16:30:00Z",
            },
        ],
    }


def create_mock_pending_dispute_detail():
    """Create mock dispute detail for PENDING status."""
    return {
        "success": True,
        "message": "Dispute retrieved successfully",
        "data": {
            "id": 1,
            "transaction_reference": "TXN_TESTUSER_OWNER1_0001",
            "vas_service": "AIRTIME",
            "vas_service_display": "Airtime",
            "amount": "1500.00",
            "charge": "22.50",
            "stamp_duty": "50.00",
            "previous_balance": "10000.00",
            "new_balance": "8427.50",
            "transaction_date": "2024-01-15T10:25:00Z",
            "status": "PENDING",
            "status_display": "Pending",
            "merchant_name": "Test Business 1",
            "message": "Transaction failed but amount was debited from my account.\
                Please investigate and refund.",
            "created_by_name": "Business Owner1",
            "created_by_email": "<EMAIL>",
            "business_name": "Test Business 1",
            "resolved_by_name": None,
            "resolved_at": None,
            "resolution_notes": None,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "timeline": {
                "created_at": "2024-01-15T10:30:00Z",
                "status": "PENDING",
                "notes": [],
            },
        },
    }


def create_mock_in_review_dispute_detail():
    """Create mock dispute detail for IN_REVIEW status."""
    return {
        "success": True,
        "message": "Dispute retrieved successfully",
        "data": {
            "id": 2,
            "transaction_reference": "TXN_TESTUSER_OWNER1_0002",
            "vas_service": "ELECTRICITY",
            "vas_service_display": "Electricity",
            "amount": "2500.00",
            "charge": "37.50",
            "stamp_duty": "50.00",
            "previous_balance": "15000.00",
            "new_balance": "12412.50",
            "transaction_date": "2024-01-14T14:15:00Z",
            "status": "IN_REVIEW",
            "status_display": "In Review",
            "merchant_name": "Test Business 1",
            "message": "I was charged twice for the same transaction. The second charge should be reversed.",
            "created_by_name": "Business Owner1",
            "created_by_email": "<EMAIL>",
            "business_name": "Test Business 1",
            "resolved_by_name": None,
            "resolved_at": None,
            "resolution_notes": None,
            "created_at": "2024-01-14T14:20:00Z",
            "updated_at": "2024-01-15T09:15:00Z",
            "timeline": {
                "created_at": "2024-01-14T14:20:00Z",
                "status": "IN_REVIEW",
                "notes": [
                    {
                        "message": "Thank you for reporting this issue.\
                            We are currently investigating your transaction.",
                        "created_at": "2024-01-15T09:15:00Z",
                        "responder_name": "Admin Support",
                        "status_when_added": "IN_REVIEW",
                    }
                ],
            },
        },
    }


def create_mock_resolved_dispute_detail():
    """Create mock dispute detail for RESOLVED status."""
    return {
        "success": True,
        "message": "Dispute retrieved successfully",
        "data": {
            "id": 3,
            "transaction_reference": "TXN_TESTUSER_OWNER1_0003",
            "vas_service": "DATA",
            "vas_service_display": "Data",
            "amount": "1000.00",
            "charge": "15.00",
            "stamp_duty": "0.00",
            "previous_balance": "8000.00",
            "new_balance": "6985.00",
            "transaction_date": "2024-01-13T08:40:00Z",
            "status": "RESOLVED",
            "status_display": "Resolved",
            "merchant_name": "Test Business 1",
            "message": "The transaction was successful but the service was not delivered.\
                Need immediate resolution.",
            "created_by_name": "Business Owner1",
            "created_by_email": "<EMAIL>",
            "business_name": "Test Business 1",
            "resolved_by_name": "Admin Support",
            "resolved_at": "2024-01-16T16:30:00Z",
            "resolution_notes": "After investigation, \
                we found the issue and have processed a refund to your account.",
            "created_at": "2024-01-13T08:45:00Z",
            "updated_at": "2024-01-16T16:30:00Z",
            "timeline": {
                "created_at": "2024-01-13T08:45:00Z",
                "status": "RESOLVED",
                "notes": [
                    {
                        "message": "We have received your dispute and our team is looking into the matter.",
                        "created_at": "2024-01-14T10:20:00Z",
                        "responder_name": "Admin Support",
                        "status_when_added": "IN_REVIEW",
                    },
                    {
                        "message": "Your case has been escalated to our technical team for further investigation.",
                        "created_at": "2024-01-15T14:45:00Z",
                        "responder_name": "Admin Support",
                        "status_when_added": "IN_REVIEW",
                    },
                    {
                        "message": "After investigation, \
                            we found the issue and have processed a refund to your account.",
                        "created_at": "2024-01-16T16:25:00Z",
                        "responder_name": "Admin Support",
                        "status_when_added": "RESOLVED",
                    },
                ],
                "resolved_at": "2024-01-16T16:30:00Z",
                "resolved_by": "Admin Support",
                "resolution_notes": "After investigation, \
                    we found the issue and have processed a refund to your account.",
            },
        },
    }


def print_json_pretty(data, title):
    """Print JSON data in a formatted way."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")
    print(json.dumps(data, indent=2, default=str))


def demonstrate_timeline_feature():
    """Demonstrate the enhanced dispute timeline feature."""
    print("Enhanced Dispute Timeline Feature Demonstration")
    print("=" * 60)

    # 1. Show enhanced dispute list with message field
    dispute_list = create_mock_dispute_list()
    print_json_pretty(dispute_list, "DISPUTE LIST - Now includes merchant message")

    # 2. Show PENDING dispute detail
    pending_dispute = create_mock_pending_dispute_detail()
    print_json_pretty(pending_dispute, "PENDING DISPUTE DETAIL - Creation time only")

    # 3. Show IN_REVIEW dispute detail
    in_review_dispute = create_mock_in_review_dispute_detail()
    print_json_pretty(
        in_review_dispute, "IN_REVIEW DISPUTE DETAIL - Creation time + notes"
    )

    # 4. Show RESOLVED dispute detail
    resolved_dispute = create_mock_resolved_dispute_detail()
    print_json_pretty(resolved_dispute, "RESOLVED DISPUTE DETAIL - Complete timeline")


if __name__ == "__main__":
    demonstrate_timeline_feature()
