from rest_framework import permissions

from .enums import ADMIN_ROLE_PERMISSIONS, AdminDisputePermission, AdminRole
from .models import AdminTeamMember


class IsAdminTeamMember(permissions.BasePermission):
    """Allows access only to admin team members."""

    message = "Only admin team members are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and hasattr(request.user, "admin_team_profile")
            and request.user.admin_team_profile.is_active
        )


class HasAdminDisputePermission(permissions.BasePermission):
    """Check if admin user has specific dispute permission based on their role."""

    def __init__(self, required_permission):
        self.required_permission = required_permission
        super().__init__()

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        # Get user's admin role
        admin_role = self._get_user_admin_role(request.user)
        if not admin_role:
            return False

        # Check if role has required permission
        role_permissions = ADMIN_ROLE_PERMISSIONS.get(admin_role, [])
        return self.required_permission in role_permissions

    def _get_user_admin_role(self, user):
        """Get user's admin role."""
        try:
            admin_profile = AdminTeamMember.objects.get(user=user, is_active=True)
            return getattr(AdminRole, admin_profile.role)
        except (AdminTeamMember.DoesNotExist, AttributeError):
            return None


class CanReadAllDisputes(HasAdminDisputePermission):
    """Permission to read all disputes across businesses."""

    def __init__(self):
        super().__init__(AdminDisputePermission.READ_ALL_DISPUTES)


class CanUpdateDisputes(HasAdminDisputePermission):
    """Permission to update disputes."""

    def __init__(self):
        super().__init__(AdminDisputePermission.UPDATE_DISPUTES)


class CanAssignDisputes(HasAdminDisputePermission):
    """Permission to assign disputes to team members."""

    def __init__(self):
        super().__init__(AdminDisputePermission.ASSIGN_DISPUTES)


def get_user_admin_context(user):
    """Helper function to get user's admin context."""
    if not user or not user.is_authenticated:
        return None, None

    try:
        admin_profile = AdminTeamMember.objects.get(user=user, is_active=True)
        role = getattr(AdminRole, admin_profile.role, None)
        return admin_profile, role
    except AdminTeamMember.DoesNotExist:
        return None, None


def check_admin_dispute_permission(user, permission):
    """Check if admin user has specific dispute permission."""
    _, role = get_user_admin_context(user)

    if not role:
        return False

    # Check role permissions
    role_permissions = ADMIN_ROLE_PERMISSIONS.get(role, [])
    return permission in role_permissions


def can_user_be_assigned_disputes(user):
    """Check if user can be assigned disputes (has update permission)."""
    return check_admin_dispute_permission(user, AdminDisputePermission.UPDATE_DISPUTES)
