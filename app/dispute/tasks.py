from celery import shared_task
from django.template.loader import get_template
from user.utils import send_email

from .models import Dispute, DisputeAssignment


@shared_task
def send_dispute_assignment_email(assignment_id: int):
    """
    Send email notification when a dispute is assigned to a team member.
    """
    try:
        assignment = DisputeAssignment.objects.select_related(
            "dispute", "assigned_to", "assigned_by", "dispute__business"
        ).get(id=assignment_id)

        dispute = assignment.dispute

        # Prepare email data
        email_data = {
            "assignee_name": assignment.assigned_to.fullname,
            "assigner_name": assignment.assigned_by.fullname,
            "dispute_id": dispute.id,
            "transaction_reference": dispute.transaction_reference,
            "business_name": dispute.business.name or "Unknown Business",
            "merchant_name": dispute.merchant_name,
            "vas_service": dispute.get_vas_service_display(),
            "amount": dispute.amount,
            "dispute_message": dispute.message,
            "assignment_notes": assignment.notes or "No additional notes provided.",
            "created_at": dispute.created_at,
            "assignment_date": assignment.created_at,
        }

        # Load email templates
        html_template = get_template("emails/dispute_assignment_template.html")
        text_template = get_template("emails/dispute_assignment_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Send email
        subject = f"Dispute Assignment - Ticket #{dispute.id}"
        send_email(subject, assignment.assigned_to.email, html_content, text_content)

        print(f"Dispute assignment email sent for assignment {assignment_id}")

    except DisputeAssignment.DoesNotExist:
        print(f"Dispute assignment {assignment_id} not found")
    except Exception as exc:
        print(
            f"Error sending dispute assignment email for assignment {assignment_id}: {str(exc)}"
        )


@shared_task
def send_dispute_status_update_email(dispute_id: str, response_id: int):
    """
    Send email notification when dispute status is updated.
    """
    try:
        from .models import DisputeResponse

        response = DisputeResponse.objects.select_related(
            "dispute", "responder", "dispute__business", "dispute__created_by"
        ).get(id=response_id)

        dispute = response.dispute

        # Only send email for non-internal notes
        if response.is_internal_note:
            return

        # Prepare email data
        email_data = {
            "merchant_name": dispute.created_by.fullname,
            "business_name": dispute.business.name or "Your Business",
            "dispute_id": dispute.id,
            "transaction_reference": dispute.transaction_reference,
            "vas_service": dispute.get_vas_service_display(),
            "amount": dispute.amount,
            "previous_status": response.get_previous_status_display(),
            "new_status": response.get_new_status_display(),
            "response_message": response.message,
            "responder_name": response.responder.fullname,
            "response_date": response.created_at,
        }

        # Load email templates
        html_template = get_template("emails/dispute_status_update_template.html")
        text_template = get_template("emails/dispute_status_update_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Send email to the merchant who created the dispute
        subject = f"Dispute Update - Ticket #{dispute.id}"
        send_email(subject, dispute.created_by.email, html_content, text_content)

        print(f"Dispute status update email sent for dispute {dispute_id}")

    except Exception as exc:
        print(
            f"Error sending dispute status update email for dispute {dispute_id}: {str(exc)}"
        )


@shared_task
def send_dispute_resolved_email(dispute_id: str):
    """
    Send email notification when dispute is resolved.
    """
    try:
        dispute = Dispute.objects.select_related(
            "business", "created_by", "resolved_by"
        ).get(id=dispute_id)

        # Prepare email data
        email_data = {
            "merchant_name": dispute.created_by.fullname,
            "business_name": dispute.business.name or "Your Business",
            "dispute_id": dispute.id,
            "transaction_reference": dispute.transaction_reference,
            "vas_service": dispute.get_vas_service_display(),
            "amount": dispute.amount,
            "resolution_notes": dispute.resolution_notes
            or "Your dispute has been resolved.",
            "resolved_by": (
                dispute.resolved_by.fullname if dispute.resolved_by else "Support Team"
            ),
            "resolved_at": dispute.resolved_at,
            "original_message": dispute.message,
        }

        # Load email templates
        html_template = get_template("emails/dispute_resolved_template.html")
        text_template = get_template("emails/dispute_resolved_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Send email to the merchant who created the dispute
        subject = f"Dispute Resolved - Ticket #{dispute.id}"
        send_email(subject, dispute.created_by.email, html_content, text_content)

        print(f"Dispute resolved email sent for dispute {dispute_id}")

    except Dispute.DoesNotExist:
        print(f"Dispute {dispute_id} not found")
    except Exception as exc:
        print(
            f"Error sending dispute resolved email for dispute {dispute_id}: {str(exc)}"
        )
