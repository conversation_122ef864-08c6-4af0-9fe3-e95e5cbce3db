from django.contrib import admin
from django.utils.html import format_html

from .models import (
    AdminTeamMember,
    BusinessMember,
    Dispute,
    DisputeAssignment,
    DisputeResponse,
)


@admin.register(BusinessMember)
class BusinessMemberAdmin(admin.ModelAdmin):
    list_display = [
        "business_name",
        "user_email",
        "user_name",
        "role",
        "is_active",
        "added_by_name",
        "created_at",
    ]
    list_filter = ["role", "is_active", "created_at"]
    search_fields = [
        "business__name",
        "user__email",
        "user__firstname",
        "user__lastname",
        "role",
    ]
    readonly_fields = ["created_at", "updated_at"]

    def business_name(self, obj):
        return obj.business.name or "Unnamed Business"

    business_name.short_description = "Business"

    def user_email(self, obj):
        return obj.user.email

    user_email.short_description = "User Email"

    def user_name(self, obj):
        return obj.user.fullname

    user_name.short_description = "User Name"

    def added_by_name(self, obj):
        return obj.added_by.fullname if obj.added_by else "-"

    added_by_name.short_description = "Added By"


@admin.register(Dispute)
class DisputeAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "transaction_reference",
        "business_name",
        "vas_service",
        "amount",
        "status_badge",
        "merchant_name",
        "created_by_name",
        "created_at",
    ]
    list_filter = ["status", "vas_service", "created_at", "transaction_date"]
    search_fields = [
        "transaction_reference",
        "business__name",
        "merchant_name",
        "created_by__email",
        "message",
    ]
    readonly_fields = ["created_at", "updated_at"]

    fieldsets = (
        (
            "Transaction Details",
            {
                "fields": (
                    "transaction_reference",
                    "vas_service",
                    "amount",
                    "charge",
                    "stamp_duty",
                    "previous_balance",
                    "new_balance",
                    "transaction_date",
                )
            },
        ),
        (
            "Dispute Information",
            {
                "fields": (
                    "business",
                    "created_by",
                    "merchant_name",
                    "message",
                    "status",
                )
            },
        ),
        (
            "Resolution",
            {
                "fields": ("resolved_by", "resolved_at", "resolution_notes"),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def business_name(self, obj):
        return obj.business.name or "Unnamed Business"

    business_name.short_description = "Business"

    def created_by_name(self, obj):
        return obj.created_by.fullname

    created_by_name.short_description = "Created By"

    def status_badge(self, obj):
        colors = {"PENDING": "#ffc107", "IN_REVIEW": "#17a2b8", "RESOLVED": "#28a745"}
        color = colors.get(obj.status, "#6c757d")
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px;">{}</span>',
            color,
            obj.status.replace("_", " "),
        )

    status_badge.short_description = "Status"


@admin.register(AdminTeamMember)
class AdminTeamMemberAdmin(admin.ModelAdmin):
    list_display = [
        "user_email",
        "user_name",
        "role",
        "is_active",
        "added_by_name",
        "created_at",
    ]
    list_filter = ["role", "is_active", "created_at"]
    search_fields = ["user__email", "user__firstname", "user__lastname", "role"]
    readonly_fields = ["created_at", "updated_at"]

    def user_email(self, obj):
        return obj.user.email

    user_email.short_description = "User Email"

    def user_name(self, obj):
        return obj.user.fullname

    user_name.short_description = "User Name"

    def added_by_name(self, obj):
        return obj.added_by.fullname if obj.added_by else "-"

    added_by_name.short_description = "Added By"


@admin.register(DisputeAssignment)
class DisputeAssignmentAdmin(admin.ModelAdmin):
    list_display = [
        "dispute_id",
        "dispute_reference",
        "assigned_to_name",
        "assigned_by_name",
        "is_active",
        "created_at",
    ]
    list_filter = ["is_active", "created_at"]
    search_fields = [
        "dispute__transaction_reference",
        "assigned_to__email",
        "assigned_to__firstname",
        "assigned_to__lastname",
    ]
    readonly_fields = ["created_at", "updated_at"]

    def dispute_id(self, obj):
        return obj.dispute.id

    dispute_id.short_description = "Dispute ID"

    def dispute_reference(self, obj):
        return obj.dispute.transaction_reference

    dispute_reference.short_description = "Transaction Reference"

    def assigned_to_name(self, obj):
        return obj.assigned_to.fullname

    assigned_to_name.short_description = "Assigned To"

    def assigned_by_name(self, obj):
        return obj.assigned_by.fullname

    assigned_by_name.short_description = "Assigned By"


@admin.register(DisputeResponse)
class DisputeResponseAdmin(admin.ModelAdmin):
    list_display = [
        "dispute_id",
        "dispute_reference",
        "responder_name",
        "previous_status",
        "new_status",
        "is_internal_note",
        "created_at",
    ]
    list_filter = ["previous_status", "new_status", "is_internal_note", "created_at"]
    search_fields = [
        "dispute__transaction_reference",
        "responder__email",
        "responder__firstname",
        "responder__lastname",
        "message",
    ]
    readonly_fields = ["created_at", "updated_at"]

    def dispute_id(self, obj):
        return obj.dispute.id

    dispute_id.short_description = "Dispute ID"

    def dispute_reference(self, obj):
        return obj.dispute.transaction_reference

    dispute_reference.short_description = "Transaction Reference"

    def responder_name(self, obj):
        return obj.responder.fullname

    responder_name.short_description = "Responder"
