"""
Django management command to seed sample general dispute data.
Usage: python manage.py seed_general_disputes
"""

import random
from datetime import timed<PERSON><PERSON>

from business.models import Business
from dispute.enums import DisputeStatus, DisputeType
from dispute.models import Dispute
from django.core.management.base import BaseCommand
from django.utils import timezone
from user.models import User


class Command(BaseCommand):
    help = "Seed sample general dispute data for testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=10,
            help="Number of general disputes to create (default: 10)",
        )
        parser.add_argument(
            "--business-email",
            type=str,
            help="Email of specific business owner to create disputes for",
        )

    def handle(self, *args, **options):
        count = options["count"]
        business_email = options.get("business_email")

        self.stdout.write(
            self.style.SUCCESS(f"Starting to seed {count} general disputes...")
        )

        # Sample dispute topics and messages
        sample_disputes = [
            {
                "topic": "Account Access Issues",
                "message": 'I have been unable to access my business dashboard for the past 3 days. \
                    The login page keeps showing an error message saying "Invalid credentials" even \
                        though I am using the correct email and password. \
                        I have tried resetting my password multiple times but the issue persists. \
                            This is affecting my business operations as I cannot monitor transactions or \
                                manage my account settings.',
            },
            {
                "topic": "Payment Processing Delays",
                "message": "My customers are reporting that their payments are taking \
                    much longer than usual to process.\
                    Normally, payments are processed within 2-3 minutes, \
                        but now they are taking 15-20 minutes or sometimes failing completely. \
                        This started happening 2 days ago and is seriously affecting my business \
                            reputation and customer satisfaction.",
            },
            {
                "topic": "Dashboard Display Errors",
                "message": "The transaction summary chart on my dashboard is not displaying correctly.\
                    The numbers shown do not match the actual transaction data when I download the detailed reports. \
                        The chart shows significantly lower amounts than what is actually recorded in the system. \
                            This is making it difficult to track my business performance accurately.",
            },
            {
                "topic": "API Integration Problems",
                "message": "Our API integration has been experiencing frequent timeouts \
                    and connection errors since last week. \
                    Our development team has confirmed that our code has not changed, \
                        so this appears to be an issue on your end. \
                        The error rate has increased from less than 1% to over 15%, which is unacceptable \
                            for our production environment.",
            },
            {
                "topic": "Commission Calculation Discrepancies",
                "message": "I have noticed discrepancies in the commission calculations for my VAS transactions. \
                    The commission rates being applied do not match what was agreed upon in our contract. \
                        Some transactions are showing higher commission deductions while others show lower amounts. \
                        I need this to be investigated and corrected immediately.",
            },
            {
                "topic": "Notification System Failure",
                "message": "I am not receiving email notifications for successful transactions, \
                    failed transactions, or low wallet balance alerts. \
                        This has been happening for the past week. \
                            I have checked my email settings and spam folder, \
                        but nothing is being received. These notifications are crucial \
                            for my business operations.",
            },
            {
                "topic": "Wallet Balance Inconsistencies",
                "message": "My wallet balance is showing inconsistent amounts across \
                    different pages of the dashboard. \
                    The main dashboard shows one amount, the wallet page shows a different \
                        amount, and the transaction history suggests yet another balance. \
                        This is very concerning and I need immediate clarification on my actual \
                            balance.",
            },
            {
                "topic": "Report Generation Issues",
                "message": "The monthly and weekly report generation feature is not working \
                    properly. \
                    When I try to generate reports, the system either times out or generates \
                        incomplete reports with missing transaction data. \
                        I need these reports for my accounting and tax purposes, so this is quite urgent.",
            },
            {
                "topic": "Mobile App Synchronization",
                "message": "The mobile app is not synchronizing properly with the web dashboard. \
                    Transactions that appear on the web platform are not showing up in the mobile app, and vice versa. \
                        This inconsistency is making it difficult to manage my business on the go.",
            },
            {
                "topic": "Customer Support Response Time",
                "message": "I have been trying to reach customer support for over a week regarding \
                    various technical issues, \
                    but I have not received any meaningful response. The automated replies are not \
                        helpful, \
                        and no human agent has contacted me despite multiple follow-ups. \
                        This level of support is not acceptable for a business-critical service.",
            },
            {
                "topic": "Security Concerns",
                "message": "I have noticed some unusual login attempts on my account from unfamiliar\
                      IP addresses. \
                    While my account security seems intact, I am concerned about the frequency of \
                        these attempts. \
                        I would like to understand what security measures are in place\
                          and if there have been any security incidents recently.",
            },
            {
                "topic": "Feature Request - Bulk Operations",
                "message": "I would like to request the addition of bulk operation features for \
                    managing \
                    multiple transactions at once. \
                    Currently, I have to process each transaction individually, which is very \
                        time-consuming\
                          when dealing with hundreds of transactions daily. Bulk approval, rejection, \
                        and status updates would greatly improve efficiency.",
            },
        ]

        # Get businesses to create disputes for
        if business_email:
            try:
                user = User.objects.get(email=business_email)
                businesses = Business.objects.filter(owner=user)
                if not businesses.exists():
                    self.stdout.write(
                        self.style.ERROR(
                            f"No business found for user with email: {business_email}"
                        )
                    )
                    return
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"User with email {business_email} not found")
                )
                return
        else:
            businesses = Business.objects.all()[:5]  # Get first 5 businesses

        if not businesses.exists():
            self.stdout.write(
                self.style.ERROR(
                    "No businesses found. Please create some businesses first."
                )
            )
            return

        created_count = 0
        statuses = [
            DisputeStatus.PENDING.value,
            DisputeStatus.IN_REVIEW.value,
            DisputeStatus.RESOLVED.value,
        ]

        for i in range(count):
            # Select random business and dispute data
            business = random.choice(businesses)
            dispute_data = random.choice(sample_disputes)
            status = random.choice(statuses)

            # Create dispute
            dispute = Dispute.objects.create(
                business=business,
                created_by=business.owner,
                dispute_type=DisputeType.GENERAL.value,
                topic=dispute_data["topic"],
                message=dispute_data["message"],
                merchant_name=business.owner.fullname
                or business.name
                or "Unknown Merchant",
                status=status,
                created_at=timezone.now() - timedelta(days=random.randint(1, 30)),
            )

            # If resolved, add resolution details
            if status == DisputeStatus.RESOLVED.value:
                dispute.resolved_at = dispute.created_at + timedelta(
                    hours=random.randint(2, 72)
                )
                dispute.resolution_notes = f"Issue resolved. {dispute_data['topic']} has been addressed by our \
                    technical team."
                dispute.save(update_fields=["resolved_at", "resolution_notes"])

            created_count += 1
            self.stdout.write(f"Created general dispute #{dispute.id}: {dispute.topic}")

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} general disputes!"
            )
        )

        # Display summary
        total_disputes = Dispute.objects.filter(
            dispute_type=DisputeType.GENERAL.value
        ).count()
        pending_count = Dispute.objects.filter(
            dispute_type=DisputeType.GENERAL.value, status=DisputeStatus.PENDING.value
        ).count()
        in_review_count = Dispute.objects.filter(
            dispute_type=DisputeType.GENERAL.value, status=DisputeStatus.IN_REVIEW.value
        ).count()
        resolved_count = Dispute.objects.filter(
            dispute_type=DisputeType.GENERAL.value, status=DisputeStatus.RESOLVED.value
        ).count()

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("GENERAL DISPUTES SUMMARY:")
        self.stdout.write(f"Total General Disputes: {total_disputes}")
        self.stdout.write(f"Pending: {pending_count}")
        self.stdout.write(f"In Review: {in_review_count}")
        self.stdout.write(f"Resolved: {resolved_count}")
        self.stdout.write("=" * 50)
