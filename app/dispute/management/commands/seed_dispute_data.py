import random
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from business.models import Business
from dispute.enums import BusinessRole, DisputeStatus
from dispute.models import Business<PERSON>ember, Dispute, DisputeResponse
from django.core.management.base import BaseCommand
from django.utils import timezone
from transaction.enums import TransactionClassEnum
from user.models import User


class Command(BaseCommand):
    help = """Seed database with test data for disputes feature.

    Examples:
    # Create new test businesses and data
    python manage.py seed_dispute_data --businesses 3 --team-members 5 --disputes 15

    # Use existing business owner (your account)
    python manage.py seed_dispute_data --business-owner-email <EMAIL> --team-members 5 --disputes 20
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--businesses",
            type=int,
            default=3,
            help="Number of businesses to create (default: 3)",
        )
        parser.add_argument(
            "--team-members",
            type=int,
            default=5,
            help="Number of team members per business (default: 5)",
        )
        parser.add_argument(
            "--disputes",
            type=int,
            default=20,
            help="Number of disputes per business (default: 20)",
        )
        parser.add_argument(
            "--clean",
            action="store_true",
            help="Clean existing test data before seeding",
        )
        parser.add_argument(
            "--business-owner-email",
            type=str,
            help="Email of existing business owner to seed data for (e.g., <EMAIL>)",
        )

    def handle(self, *args, **options):
        if options["clean"]:
            self.stdout.write(self.style.WARNING("Cleaning existing test data..."))
            self.clean_test_data()

        business_owner_email = options.get("business_owner_email")
        businesses_count = options["businesses"]
        team_members_count = options["team_members"]
        disputes_count = options["disputes"]

        self.stdout.write(self.style.SUCCESS("Starting data seeding..."))

        # Handle existing business owner or create new businesses
        if business_owner_email:
            businesses = self.get_or_create_business_for_owner(business_owner_email)
            if not businesses:
                return
        else:
            # Create businesses with owners
            businesses = self.create_businesses(businesses_count)
            self.stdout.write(
                self.style.SUCCESS(f"Created {len(businesses)} businesses")
            )

        # Create team members for each business
        total_members = 0
        for business in businesses:
            members = self.create_team_members(business, team_members_count)
            total_members += len(members)

        self.stdout.write(self.style.SUCCESS(f"Created {total_members} team members"))

        # Create disputes for each business
        total_disputes = 0
        total_responses = 0
        for business in businesses:
            disputes = self.create_disputes(business, disputes_count)
            total_disputes += len(disputes)

            # Create sample responses for some disputes to demonstrate timeline
            responses = self.create_dispute_responses(business, disputes)
            total_responses += len(responses)

        self.stdout.write(self.style.SUCCESS(f"Created {total_disputes} disputes"))
        self.stdout.write(
            self.style.SUCCESS(f"Created {total_responses} dispute responses")
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"\n Data seeding completed successfully!\n"
                f" Summary:\n"
                f"   - Businesses: {len(businesses)}\n"
                f"   - Team Members: {total_members}\n"
                f"   - Disputes: {total_disputes}\n"
                f"   - Dispute Responses: {total_responses}\n"
            )
        )

        # Print sample login credentials
        self.print_sample_credentials(businesses)

    def clean_test_data(self):
        """Clean existing test data."""
        # Delete disputes first (due to foreign key constraints)
        Dispute.objects.filter(transaction_reference__startswith="TEST_").delete()

        # Delete business members
        BusinessMember.objects.filter(user__email__contains="testuser").delete()

        # Delete test businesses and users
        Business.objects.filter(email__contains="testbusiness").delete()

        User.objects.filter(email__contains="testuser").delete()

    def get_or_create_business_for_owner(self, email):
        """Get existing business for the specified owner email."""
        try:
            user = User.objects.get(email=email, role="Business_Owner")
            self.stdout.write(f"✓ Found existing business owner: {email}")

            if hasattr(user, "business"):
                business = user.business
                self.stdout.write(
                    f'✓ Using existing business: {business.name or "Unnamed Business"}'
                )
                return [business]
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f" User {email} exists but has no business associated"
                    )
                )
                return []

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f" Business owner with email {email} not found")
            )
            self.stdout.write(
                self.style.WARNING(
                    'Please make sure the user exists and has role "Business_Owner"'
                )
            )
            return []

    def create_businesses(self, count):
        """Create test businesses with owners."""
        businesses = []

        for i in range(1, count + 1):
            # Create business owner
            owner = User.objects.create_user(
                email=f"testuser.owner{i}@example.com",
                password="testpass123",
                firstname="Business",
                lastname=f"Owner{i}",
                role="Business_Owner",
                verified=True,
                is_active=True,
            )

            # Create business
            business = Business.objects.create(
                name=f"Test Business {i}",
                owner=owner,
                email=f"testbusiness{i}@example.com",
                description=f"Test business {i} for dispute testing",
                phone=f"+234801234567{i}",
                status="Active",
                office_address=f"{i} Test Street",
                city="Lagos",
                state="Lagos",
                postal_code="100001",
            )

            businesses.append(business)

        return businesses

    def create_team_members(self, business, count):
        """Create team members for a business."""
        members = []
        roles = [
            BusinessRole.MERCHANT_ADMIN,
            BusinessRole.CUSTOMER_SUPPORT,
            BusinessRole.OPERATIONS,
            BusinessRole.RECONCILIATION,
            BusinessRole.DEVELOPER,
        ]

        # Create unique identifier for this business
        business_email_prefix = business.owner.email.split("@")[0].replace(".", "_")

        for i in range(1, count + 1):
            role = roles[(i - 1) % len(roles)]

            # Map business role to user role
            user_role_mapping = {
                BusinessRole.MERCHANT_ADMIN: "Merchant_Admin",
                BusinessRole.CUSTOMER_SUPPORT: "Customer_Support",
                BusinessRole.OPERATIONS: "Operations",
                BusinessRole.RECONCILIATION: "Reconciliation",
                BusinessRole.DEVELOPER: "Developer",
            }

            # Create user with unique email based on business owner
            user_email = f"{business_email_prefix}.{role.value.lower()}{i}@example.com"

            # Check if user already exists
            if User.objects.filter(email=user_email).exists():
                self.stdout.write(f"   → User {user_email} already exists, skipping...")
                continue

            user = User.objects.create_user(
                email=user_email,
                password="testpass123",
                firstname=f'{role.value.replace("_", " ").title()}',
                lastname=f"User{i}",
                role=user_role_mapping.get(role, "Business_Owner"),
                verified=True,
                is_active=True,
            )

            # Create business member
            member = BusinessMember.objects.create(
                business=business,
                user=user,
                role=role.value,
                is_active=True,
                added_by=business.owner,
            )

            members.append(member)

        return members

    def create_disputes(self, business, count):
        """Create test disputes for a business."""
        disputes = []
        vas_services = [choice[0] for choice in TransactionClassEnum.choices()]
        statuses = [choice[0] for choice in DisputeStatus.choices()]

        # Get potential dispute creators (business owner + team members who can create)
        creators = [business.owner]
        team_members = BusinessMember.objects.filter(
            business=business,
            role__in=[BusinessRole.MERCHANT_ADMIN.value, BusinessRole.OPERATIONS.value],
        )
        creators.extend([member.user for member in team_members])

        # Create unique identifier for this business
        business_email_prefix = (
            business.owner.email.split("@")[0].replace(".", "_").upper()
        )

        for i in range(1, count + 1):
            # Random transaction details
            amount = Decimal(str(random.uniform(100, 10000))).quantize(Decimal("0.01"))
            charge = amount * Decimal("0.015")  # 1.5% charge
            stamp_duty = Decimal("50.00") if amount > 1000 else Decimal("0.00")
            previous_balance = Decimal(str(random.uniform(5000, 50000))).quantize(
                Decimal("0.01")
            )
            new_balance = previous_balance - amount - charge - stamp_duty

            # Random dates within last 30 days
            transaction_date = timezone.now() - timedelta(
                days=random.randint(1, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59),
            )

            dispute = Dispute.objects.create(
                business=business,
                created_by=random.choice(creators),
                transaction_reference=f"TXN_{business_email_prefix}_{i:04d}",
                vas_service=random.choice(vas_services),
                amount=amount,
                charge=charge,
                stamp_duty=stamp_duty,
                previous_balance=previous_balance,
                new_balance=new_balance,
                transaction_date=transaction_date,
                status=random.choice(statuses),
                merchant_name=business.name or f"Test Merchant {business_email_prefix}",
                message=self.generate_dispute_message(i),
            )

            # If dispute is resolved, add resolution details
            if dispute.status == DisputeStatus.RESOLVED.value:
                dispute.resolved_by = business.owner
                dispute.resolved_at = timezone.now() - timedelta(
                    days=random.randint(0, 5)
                )
                dispute.resolution_notes = f'Dispute resolved after investigation. \
                    Issue was {random.choice(["refunded", "corrected", "explained to customer"])}.'
                dispute.save()

            disputes.append(dispute)

        return disputes

    def create_dispute_responses(self, business, disputes):
        """Create sample responses for disputes to demonstrate timeline functionality."""
        responses = []

        # Create a mock admin user for responses (in real scenario, this would be actual admin users)
        admin_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "password": "adminpass123",
                "firstname": "Admin",
                "lastname": "Support",
                "role": "Admin",
                "verified": True,
                "is_active": True,
            },
        )

        # Sample response messages
        review_messages = [
            "Thank you for reporting this issue. We are currently investigating your transaction.",
            "We have received your dispute and our team is looking into the matter.",
            "Your case has been escalated to our technical team for further investigation.",
            "We are working with our payment processor to resolve this issue.",
            "Additional verification is required. Please provide more details if available.",
        ]

        resolution_messages = [
            "After investigation, we found the issue and have processed a refund to your account.",
            "The transaction has been successfully reversed. Please check your wallet balance.",
            "Issue resolved: The service has been re-delivered to the correct recipient.",
            "We have corrected the billing error and adjusted your account accordingly.",
            "The duplicate charge has been identified and removed from your account.",
        ]

        # Add responses to some disputes based on their status
        for dispute in disputes:
            if dispute.status == DisputeStatus.IN_REVIEW.value:
                # Add 1-2 review responses
                num_responses = random.randint(1, 2)
                for i in range(num_responses):
                    response_time = dispute.created_at + timedelta(
                        hours=random.randint(2, 48), minutes=random.randint(0, 59)
                    )

                    response = DisputeResponse.objects.create(
                        dispute=dispute,
                        responder=admin_user,
                        message=random.choice(review_messages),
                        previous_status=DisputeStatus.PENDING.value,
                        new_status=DisputeStatus.IN_REVIEW.value,
                        is_internal_note=False,
                        created_at=response_time,
                    )
                    responses.append(response)

            elif dispute.status == DisputeStatus.RESOLVED.value:
                # Add review responses first
                num_review_responses = random.randint(1, 2)
                last_response_time = dispute.created_at

                for i in range(num_review_responses):
                    response_time = last_response_time + timedelta(
                        hours=random.randint(2, 24), minutes=random.randint(0, 59)
                    )

                    response = DisputeResponse.objects.create(
                        dispute=dispute,
                        responder=admin_user,
                        message=random.choice(review_messages),
                        previous_status=DisputeStatus.PENDING.value,
                        new_status=DisputeStatus.IN_REVIEW.value,
                        is_internal_note=False,
                        created_at=response_time,
                    )
                    responses.append(response)
                    last_response_time = response_time

                # Add final resolution response
                resolution_time = last_response_time + timedelta(
                    hours=random.randint(4, 48), minutes=random.randint(0, 59)
                )

                resolution_response = DisputeResponse.objects.create(
                    dispute=dispute,
                    responder=admin_user,
                    message=random.choice(resolution_messages),
                    previous_status=DisputeStatus.IN_REVIEW.value,
                    new_status=DisputeStatus.RESOLVED.value,
                    is_internal_note=False,
                    created_at=resolution_time,
                )
                responses.append(resolution_response)

        return responses

    def generate_dispute_message(self, index):
        """Generate realistic dispute messages."""
        messages = [
            "Transaction failed but amount was debited from my account. Please investigate and refund.",
            "I was charged twice for the same transaction. The second charge should be reversed.",
            "The transaction was successful but the service was not delivered. Need immediate resolution.",
            "Wrong amount was charged. I was supposed to pay less but more was debited.",
            "Transaction shows as failed on my end but recipient confirms they received the service.",
            "Duplicate transaction - same service was charged multiple times in error.",
            "Service delivery failed but payment was processed. Please check and resolve.",
            "Incorrect charges applied to my transaction. The fee structure seems wrong.",
            "Transaction timeout occurred but amount was still debited from wallet.",
            "Partial service delivery - paid for full service but only received partial.",
        ]

        return messages[(index - 1) % len(messages)]

    def print_sample_credentials(self, businesses):
        """Print sample login credentials for testing."""
        self.stdout.write(
            self.style.SUCCESS(f"\n🔑 Sample Login Credentials:\n" f'{"="*50}\n')
        )

        for i, business in enumerate(businesses, 1):
            self.stdout.write(
                f"Business {i}: {business.name}\n"
                f"  Owner: {business.owner.email} / testpass123\n"
            )

            # Show first team member of each role
            team_members = BusinessMember.objects.filter(business=business)[:3]
            for member in team_members:
                self.stdout.write(
                    f"  {member.role}: {member.user.email} / testpass123\n"
                )
            self.stdout.write("")
