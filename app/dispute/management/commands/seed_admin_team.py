from dispute.enums import AdminRole
from dispute.models import AdminTeamMember
from django.core.management.base import BaseCommand
from user.models import User


class Command(BaseCommand):
    help = """Seed database with admin team members for dispute management.

    Examples:
    # Create admin team members
    python manage.py seed_admin_team --admin-email <EMAIL> --team-members 4

    # Clean existing admin team data
    python manage.py seed_admin_team --clean
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--admin-email", type=str, help="Email of the main admin user (must exist)"
        )
        parser.add_argument(
            "--team-members",
            type=int,
            default=4,
            help="Number of team members to create (default: 4)",
        )
        parser.add_argument(
            "--clean",
            action="store_true",
            help="Clean existing admin team data before seeding",
        )

    def handle(self, *args, **options):
        if options["clean"]:
            self.stdout.write(
                self.style.WARNING("Cleaning existing admin team data...")
            )
            self.clean_admin_data()
            return

        admin_email = options.get("admin_email")
        team_members_count = options["team_members"]

        if not admin_email:
            self.stdout.write(
                self.style.ERROR("Please provide --admin-email parameter")
            )
            return

        self.stdout.write(self.style.SUCCESS("Starting admin team seeding..."))

        # Create admin team
        admin_profile = self.create_admin_profile(admin_email)
        if not admin_profile:
            return

        # Create team members
        team_members = self.create_team_members(admin_profile.user, team_members_count)

        self.stdout.write(
            self.style.SUCCESS(
                f"\n✅ Admin team seeding completed successfully!\n"
                f"📊 Summary:\n"
                f"   - Admin Profile: {admin_profile.user.email}\n"
                f"   - Team Members: {len(team_members)}\n"
            )
        )

        # Print sample login credentials
        self.print_sample_credentials(admin_profile, team_members)

    def clean_admin_data(self):
        """Clean existing admin team data."""
        # Delete admin team members
        AdminTeamMember.objects.filter(user__email__contains="admin.team").delete()

        # Delete test admin users
        User.objects.filter(email__contains="admin.team").delete()

        self.stdout.write(self.style.SUCCESS("Admin team data cleaned successfully"))

    def create_admin_profile(self, admin_email):
        """Create admin profile for existing user."""
        try:
            user = User.objects.get(email=admin_email)
            self.stdout.write(f"✓ Found admin user: {admin_email}")

            # Create or update admin profile
            admin_profile, created = AdminTeamMember.objects.get_or_create(
                user=user,
                defaults={
                    "role": AdminRole.ADMIN.value,
                    "is_active": True,
                    "added_by": user,  # Self-added
                },
            )

            if created:
                self.stdout.write(f"✓ Created admin profile for: {admin_email}")
            else:
                self.stdout.write(f"✓ Admin profile already exists for: {admin_email}")

            return admin_profile

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"❌ Admin user with email {admin_email} not found")
            )
            self.stdout.write(
                self.style.WARNING("Please make sure the admin user exists first")
            )
            return None

    def create_team_members(self, admin_user, count):
        """Create admin team members."""
        team_members = []
        roles = [
            AdminRole.CUSTOMER_SUPPORT,
            AdminRole.OPERATIONS,
            AdminRole.RECONCILIATION,
            AdminRole.DEVELOPER,
        ]

        for i in range(1, count + 1):
            role = roles[(i - 1) % len(roles)]

            # Create user
            user_email = f"admin.team.{role.value.lower()}{i}@sagecloud.com"

            # Check if user already exists
            if User.objects.filter(email=user_email).exists():
                self.stdout.write(f"   → User {user_email} already exists, skipping...")
                continue

            user = User.objects.create_user(
                email=user_email,
                password="adminpass123",
                firstname=f'{role.value.replace("_", " ").title()}',
                lastname=f"Admin{i}",
                role="Admin",  # Global role
                verified=True,
                is_active=True,
            )

            # Create admin team member
            team_member = AdminTeamMember.objects.create(
                user=user, role=role.value, is_active=True, added_by=admin_user
            )

            team_members.append(team_member)
            self.stdout.write(f"   ✓ Created {role.value} team member: {user_email}")

        return team_members

    def print_sample_credentials(self, admin_profile, team_members):
        """Print sample login credentials for testing."""
        self.stdout.write(
            self.style.SUCCESS(f"\n🔑 Admin Team Login Credentials:\n" f'{"="*50}\n')
        )

        self.stdout.write(
            f"Main Admin: {admin_profile.user.email}\n"
            f"  Role: {admin_profile.role}\n"
            f"  Permissions: Read All, Update, Assign\n"
        )

        for member in team_members:
            permissions = []
            if member.role == AdminRole.ADMIN.value:
                permissions = ["Read All", "Update", "Assign"]
            elif member.role == AdminRole.OPERATIONS.value:
                permissions = ["Read All", "Update"]
            elif member.role == AdminRole.CUSTOMER_SUPPORT.value:
                permissions = ["Read All"]
            else:
                permissions = ["No Dispute Access"]

            self.stdout.write(
                f'\n{member.role.replace("_", " ").title()}: {member.user.email}\n'
                f"  Password: adminpass123\n"
                f'  Permissions: {", ".join(permissions)}\n'
            )

        self.stdout.write(
            self.style.SUCCESS(
                "\n📋 API Endpoints for Admin:\n"
                "  GET  /api/v1/admin/disputes/ - List all disputes\n"
                "  GET  /api/v1/admin/disputes/{id}/ - Get dispute details\n"
                "  POST /api/v1/admin/disputes/{id}/assign/ - Assign dispute\n"
                "  POST /api/v1/admin/disputes/{id}/add-response/ - Add response\n"
                "  GET  /api/v1/admin/disputes/statistics/ - Get statistics\n"
                "  GET  /api/v1/admin/disputes/assignable-members/ - Get assignable members\n"
            )
        )
