from decimal import Decimal

from business.models import Business
from common.models import AuditableModel
from django.db import models
from transaction.enums import TransactionClassEnum
from user.models import User

from .enums import AdminRole, BusinessRole, DisputeStatus, DisputeType


class BusinessMember(AuditableModel):
    """Model to manage business team members with specific roles"""

    business = models.ForeignKey(
        Business,
        on_delete=models.CASCADE,
        related_name="team_members",
        db_index=True,
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="business_memberships",
        db_index=True,
    )
    role = models.CharField(
        max_length=50,
        choices=BusinessRole.choices(),
        db_index=True,
    )
    is_active = models.BooleanField(default=True, db_index=True)
    added_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="added_team_members",
    )

    class Meta:
        unique_together = ("business", "user")
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.business.name or 'UNNAMED BUSINESS'} - {self.user.fullname} ({self.role})"


class Dispute(AuditableModel):
    """Model for handling both transaction and general disputes"""

    business = models.ForeignKey(
        Business,
        on_delete=models.CASCADE,
        related_name="disputes",
        db_index=True,
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="created_disputes",
        db_index=True,
    )

    # Dispute type and basic info
    dispute_type = models.CharField(
        max_length=20,
        choices=DisputeType.choices(),
        default=DisputeType.TRANSACTION.value,
        db_index=True,
        help_text="Type of dispute (Transaction or General)",
    )

    # General dispute fields
    topic = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Topic/subject for general disputes",
    )
    attachment = models.FileField(
        upload_to="dispute_attachments/%Y/%m/%d/",
        blank=True,
        null=True,
        help_text="Optional file attachment for disputes",
    )

    # Transaction details (for transaction disputes)
    transaction_reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text="Reference ID of the disputed transaction",
    )
    vas_service = models.CharField(
        max_length=30,
        choices=TransactionClassEnum.choices(),
        blank=True,
        null=True,
        db_index=True,
        help_text="VAS service type (e.g., Airtime, Electricity)",
    )
    amount = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Transaction amount",
    )
    charge = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        default=Decimal("0.00"),
        blank=True,
        null=True,
        help_text="Transaction charge/fee",
    )
    stamp_duty = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        default=Decimal("0.00"),
        blank=True,
        null=True,
        help_text="Stamp duty charged",
    )
    previous_balance = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Wallet balance before transaction",
    )
    new_balance = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Wallet balance after transaction",
    )
    transaction_date = models.DateTimeField(
        blank=True, null=True, help_text="Date and time when the transaction occurred"
    )

    # Dispute details
    status = models.CharField(
        max_length=20,
        choices=DisputeStatus.choices(),
        default=DisputeStatus.PENDING.value,
        db_index=True,
    )
    merchant_name = models.CharField(
        max_length=255, help_text="Name of the business owner/merchant"
    )
    message = models.TextField(help_text="Description of the dispute/issue")

    # Resolution tracking
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="resolved_disputes",
    )
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
        constraints = [
            models.UniqueConstraint(
                fields=["business", "transaction_reference"],
                condition=models.Q(
                    dispute_type=DisputeType.TRANSACTION.value,
                    transaction_reference__isnull=False,
                ),
                name="unique_business_transaction_dispute",
                violation_error_message="A dispute already exists for this transaction in your business.",
            )
        ]
        indexes = [
            models.Index(fields=["business", "status"]),
            models.Index(fields=["dispute_type", "status"]),
            models.Index(fields=["transaction_reference"]),
            models.Index(fields=["vas_service", "status"]),
            models.Index(fields=["business", "transaction_reference"]),
            models.Index(fields=["business", "dispute_type"]),
        ]

    def __str__(self):
        if self.dispute_type == DisputeType.TRANSACTION.value:
            return f"Dispute #{self.id} - {self.transaction_reference} ({self.status})"
        else:
            return f"General Dispute #{self.id} - {self.topic or 'No Topic'} ({self.status})"

    @property
    def is_pending(self):
        return self.status == DisputeStatus.PENDING.value

    @property
    def is_in_review(self):
        return self.status == DisputeStatus.IN_REVIEW.value

    @property
    def is_resolved(self):
        return self.status == DisputeStatus.RESOLVED.value

    @property
    def is_transaction_dispute(self):
        return self.dispute_type == DisputeType.TRANSACTION.value

    @property
    def is_general_dispute(self):
        return self.dispute_type == DisputeType.GENERAL.value

    @property
    def display_title(self):
        """Return a display-friendly title for the dispute"""
        if self.is_transaction_dispute:
            return f"Transaction Dispute - {self.transaction_reference}"
        else:
            return f"General Dispute - {self.topic or 'No Topic'}"


class AdminTeamMember(AuditableModel):
    """Model to manage admin team members for dispute resolution"""

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="admin_team_profile",
        db_index=True,
    )
    role = models.CharField(
        max_length=50,
        choices=AdminRole.choices(),
        db_index=True,
    )
    is_active = models.BooleanField(default=True, db_index=True)
    added_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="added_admin_members",
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.fullname} ({self.role})"


class DisputeAssignment(AuditableModel):
    """Model to track dispute assignments to admin team members"""

    dispute = models.ForeignKey(
        Dispute,
        on_delete=models.CASCADE,
        related_name="assignments",
        db_index=True,
    )
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="assigned_disputes",
        db_index=True,
    )
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="dispute_assignments_made",
        db_index=True,
    )
    is_active = models.BooleanField(default=True, db_index=True)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["dispute", "is_active"]),
            models.Index(fields=["assigned_to", "is_active"]),
        ]

    def __str__(self):
        return f"Dispute {self.dispute.id} assigned to {self.assigned_to.fullname}"


class DisputeResponse(AuditableModel):
    """Model to track responses/updates made to disputes by admin team"""

    dispute = models.ForeignKey(
        Dispute,
        on_delete=models.CASCADE,
        related_name="responses",
        db_index=True,
    )
    responder = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="dispute_responses",
        db_index=True,
    )
    message = models.TextField(help_text="Response message from admin team")
    previous_status = models.CharField(
        max_length=20,
        choices=DisputeStatus.choices(),
        help_text="Status before this response",
    )
    new_status = models.CharField(
        max_length=20,
        choices=DisputeStatus.choices(),
        help_text="Status after this response",
    )
    is_internal_note = models.BooleanField(
        default=False,
        help_text="Whether this is an internal note (not visible to merchant)",
    )

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"Response to Dispute {self.dispute.id} by {self.responder.fullname}"
