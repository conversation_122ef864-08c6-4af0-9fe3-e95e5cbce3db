from decimal import Decimal

import pytest
from business.tests.fixtures import create_business, create_test_user
from django.db import IntegrityError
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from transaction.models import Transaction
from wallet.enums import WalletEnums
from wallet.models import Wallet

from .enums import DisputeStatus
from .models import Dispute


@pytest.mark.django_db
class TestDisputeDuplicatePrevention:
    """Test cases for preventing duplicate disputes for the same transaction"""

    @pytest.fixture
    def api_client(self):
        """Return an API client for testing"""
        return APIClient()

    @pytest.fixture
    def authenticated_client(self, api_client):
        """Return an authenticated API client with user and business"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)
        api_client.force_authenticate(user=user)
        return api_client, user, business

    @pytest.fixture
    def sample_transaction(self, authenticated_client):
        """Create a sample transaction for testing"""
        _, _, business = authenticated_client

        # Create a wallet for the business
        wallet = Wallet.objects.create(
            business=business, type=WalletEnums.GENERAL, balance=Decimal("5000.00")
        )

        # Create a transaction for testing
        transaction = Transaction.objects.create(
            wallet=wallet,
            business=business,
            reference="TXN-TEST-001",
            merchant_reference="MERCH-TEST-001",
            amount=Decimal("1000.00"),
            charge=Decimal("50.00"),
            old_balance=Decimal("5000.00"),
            new_balance=Decimal("3950.00"),
            txn_class="AIRTIME",
            status="SUCCESS",
            mode="API",
            type="DEBIT",
            narration="Test transaction for dispute",
        )
        return transaction

    @pytest.fixture
    def sample_dispute_data(self, sample_transaction):
        """Return sample dispute data"""
        return {
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at.isoformat(),
            "merchant_name": sample_transaction.business.name,
            "message": "This is a test dispute message for duplicate prevention testing.",
        }

    def test_create_first_dispute_success(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that the first dispute for a transaction can be created successfully"""
        api_client, user, business = authenticated_client

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, sample_dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["success"] is True
        assert "Dispute created successfully" in response.data["message"]

        # Verify dispute was created in database
        dispute = Dispute.objects.get(
            business=business,
            transaction_reference=sample_dispute_data["transaction_reference"],
        )
        assert dispute.status == DisputeStatus.PENDING.value
        assert dispute.created_by == user

    def test_create_duplicate_dispute_fails_validation(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that creating a duplicate dispute fails at serializer validation level"""
        api_client, user, business = authenticated_client

        # Create first dispute
        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, sample_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate dispute
        second_response = api_client.post(url, sample_dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "transaction_reference" in second_response.data
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business,
            transaction_reference=sample_dispute_data["transaction_reference"],
        ).count()
        assert dispute_count == 1

    def test_create_duplicate_dispute_from_reference_fails(
        self, authenticated_client, sample_transaction
    ):
        """Test that creating duplicate dispute using create-from-reference endpoint fails"""
        api_client, _, business = authenticated_client

        # Create first dispute using create-from-reference endpoint
        url = reverse("dispute:dispute-create-from-reference")
        dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "message": "This is a test dispute message for duplicate prevention testing.",
        }

        first_response = api_client.post(url, dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate dispute using same endpoint
        second_response = api_client.post(url, dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "transaction_reference" in second_response.data
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1

    def test_database_constraint_prevents_duplicate(
        self, authenticated_client, sample_transaction
    ):
        """Test that database constraint prevents duplicate disputes"""
        _, user, business = authenticated_client

        # Create first dispute directly in database
        dispute_data = {
            "business": business,
            "created_by": user,
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at,
            "merchant_name": sample_transaction.business.name,
            "message": "First dispute",
            "status": DisputeStatus.PENDING.value,
        }

        first_dispute = Dispute.objects.create(**dispute_data)
        assert first_dispute.id is not None

        # Attempt to create duplicate dispute directly in database
        dispute_data["message"] = "Second dispute (should fail)"

        with pytest.raises(IntegrityError):
            Dispute.objects.create(**dispute_data)

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1

    def test_different_businesses_can_create_disputes_for_same_reference(
        self, api_client
    ):
        """Test that different businesses can create disputes for the same transaction reference"""
        # Create first business and user
        user1 = create_test_user(email="<EMAIL>")
        business1 = create_business(owner=user1, name="Business 1")

        # Create second business and user
        user2 = create_test_user(email="<EMAIL>")
        business2 = create_business(owner=user2, name="Business 2")

        # Same transaction reference (could happen in real world)
        transaction_reference = "TXN-SHARED-001"

        # Create dispute for first business
        api_client.force_authenticate(user=user1)
        dispute_data1 = {
            "transaction_reference": transaction_reference,
            "vas_service": "AIRTIME",
            "amount": Decimal("1000.00"),
            "charge": Decimal("50.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("5000.00"),
            "new_balance": Decimal("3950.00"),
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": business1.name,
            "message": "Dispute from business 1",
        }

        url = reverse("dispute:dispute-list")
        response1 = api_client.post(url, dispute_data1, format="json")
        assert response1.status_code == status.HTTP_201_CREATED

        # Create dispute for second business with same transaction reference
        api_client.force_authenticate(user=user2)
        dispute_data2 = {
            "transaction_reference": transaction_reference,
            "vas_service": "ELECTRICITY",
            "amount": Decimal("2000.00"),
            "charge": Decimal("100.00"),
            "stamp_duty": Decimal("50.00"),
            "previous_balance": Decimal("10000.00"),
            "new_balance": Decimal("7850.00"),
            "transaction_date": "2024-01-01T11:00:00Z",
            "merchant_name": business2.name,
            "message": "Dispute from business 2",
        }

        response2 = api_client.post(url, dispute_data2, format="json")
        assert response2.status_code == status.HTTP_201_CREATED

        # Verify both disputes exist
        dispute1 = Dispute.objects.get(
            business=business1, transaction_reference=transaction_reference
        )
        dispute2 = Dispute.objects.get(
            business=business2, transaction_reference=transaction_reference
        )

        assert dispute1.id != dispute2.id
        assert dispute1.business != dispute2.business
        assert dispute1.message != dispute2.message

    def test_error_message_includes_existing_dispute_details(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that error message includes details about the existing dispute"""
        api_client, _, _ = authenticated_client

        # Create first dispute
        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, sample_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        created_dispute = Dispute.objects.get(
            transaction_reference=sample_dispute_data["transaction_reference"]
        )

        # Attempt to create duplicate dispute
        second_response = api_client.post(url, sample_dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        error_message = str(second_response.data["transaction_reference"])

        # Verify error message contains dispute ID and status
        assert created_dispute.id in error_message
        assert created_dispute.status in error_message
        assert sample_dispute_data["transaction_reference"] in error_message

    def test_mixed_endpoint_duplicate_prevention(
        self, authenticated_client, sample_transaction
    ):
        """Test that duplicate prevention works across different endpoints"""
        api_client, _, business = authenticated_client

        # Create dispute using full endpoint
        full_dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at.isoformat(),
            "merchant_name": sample_transaction.business.name,
            "message": "First dispute via full endpoint",
        }

        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, full_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate using create-from-reference endpoint
        reference_dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "message": "Second dispute via reference endpoint (should fail)",
        }

        reference_url = reverse("dispute:dispute-create-from-reference")
        second_response = api_client.post(
            reference_url, reference_dispute_data, format="json"
        )

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1
