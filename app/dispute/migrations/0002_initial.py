# Generated by Django 5.1.7 on 2025-06-22 18:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0002_initial"),
        ("dispute", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="adminteammember",
            name="added_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="added_admin_members",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="adminteammember",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="admin_team_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="businessmember",
            name="added_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="added_team_members",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="businessmember",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="team_members",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="businessmember",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="business_memberships",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="dispute",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="disputes",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="dispute",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_disputes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="dispute",
            name="resolved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="resolved_disputes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="disputeassignment",
            name="assigned_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="dispute_assignments_made",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="disputeassignment",
            name="assigned_to",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="assigned_disputes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="disputeassignment",
            name="dispute",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="assignments",
                to="dispute.dispute",
            ),
        ),
        migrations.AddField(
            model_name="disputeresponse",
            name="dispute",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="responses",
                to="dispute.dispute",
            ),
        ),
        migrations.AddField(
            model_name="disputeresponse",
            name="responder",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="dispute_responses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="businessmember",
            unique_together={("business", "user")},
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["business", "status"], name="dispute_dis_busines_f3a38d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["dispute_type", "status"], name="dispute_dis_dispute_8ca33a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["transaction_reference"], name="dispute_dis_transac_71d620_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["vas_service", "status"], name="dispute_dis_vas_ser_2815f2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["business", "transaction_reference"],
                name="dispute_dis_busines_8f204b_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="dispute",
            index=models.Index(
                fields=["business", "dispute_type"],
                name="dispute_dis_busines_468be4_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="dispute",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("dispute_type", "TRANSACTION"),
                    ("transaction_reference__isnull", False),
                ),
                fields=("business", "transaction_reference"),
                name="unique_business_transaction_dispute",
                violation_error_message="A dispute already exists for this transaction in your business.",
            ),
        ),
        migrations.AddIndex(
            model_name="disputeassignment",
            index=models.Index(
                fields=["dispute", "is_active"], name="dispute_dis_dispute_5d244e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="disputeassignment",
            index=models.Index(
                fields=["assigned_to", "is_active"],
                name="dispute_dis_assigne_60af33_idx",
            ),
        ),
    ]
