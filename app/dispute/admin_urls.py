from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .admin_views import AdminDisputeViewSet, AdminTeamMemberViewSet

app_name = "dispute_admin"

router = DefaultRouter()
router.register("team-members", AdminTeamMemberViewSet, basename="admin-team-member")
router.register("", AdminDisputeViewSet, basename="admin-dispute")

urlpatterns = [
    path("", include(router.urls)),
]
