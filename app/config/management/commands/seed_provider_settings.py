from common.enums import VenderEnum
from config.models import ProviderSettings
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.stdout.write("Populating venders...")

        created_count = 0
        skipped_count = 0

        for vender in VenderEnum:
            services = vender.services()
            for product_value, provider_value in services:
                obj, created = ProviderSettings.objects.get_or_create(
                    product=product_value,
                    provider=provider_value,
                    vender=vender.value,
                    defaults={"is_active": True},
                )
                if created:
                    created_count += 1
                else:
                    skipped_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Seed complete! Created: {created_count}, Skipped (already exists): {skipped_count}"
            )
        )
