import csv
import os

from config.models import Bank
from django.apps import apps
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = "Import banks from CSV"

    def handle(self, *args, **options):
        base_dir = apps.get_app_config("config").path
        csv_path = os.path.join(base_dir, "data", "unique_updated_banks.csv")

        with open(csv_path, newline="", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                name = row.get("name", "").strip()
                institution_code = row.get("institution_code", "").strip() or None
                bank_code = row.get("bank_code", "").strip() or None

                if not name:
                    continue  # Skip invalid rows

                bank, created = Bank.objects.update_or_create(
                    institution_code=institution_code,
                    defaults={"name": name, "bank_code": bank_code},
                )
                action = "Created" if created else "Updated"
                self.stdout.write(f"{action}: {name}")

        self.stdout.write(self.style.SUCCESS("Bank CSV import completed."))
