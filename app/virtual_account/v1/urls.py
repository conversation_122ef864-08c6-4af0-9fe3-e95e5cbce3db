from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import VirtualAccountTransactionsViewSet, VirtualAccountViewSet

app_name = "virtual_account"

router = DefaultRouter()
router.register(
    "transactions",
    VirtualAccountTransactionsViewSet,
    basename="virtual_account_transactions",
)
router.register("", VirtualAccountViewSet, basename="virtual_account")

urlpatterns = [
    path("", include(router.urls)),
]
