from rest_framework import serializers
from transaction.models.virtual_account import VirtualAccountVasTransaction
from virtual_account.models import VirtualAccount


class VirtualAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = VirtualAccount
        fields = "__all__"


class VirtualAccountTransactionSerializer(serializers.ModelSerializer):
    business_name = serializers.CharField(source="business.name", read_only=True)

    class Meta:
        model = VirtualAccountVasTransaction
        fields = [
            "session_id",
            "source_account_number",
            "source_account_name",
            "source_bank_name",
            "source_bank_code",
            "recipient_account_number",
            "recipient_account_name",
            "recipient_bank_name",
            "recipient_bank_code",
            "reference",
            "merchant_reference",
            "status",
            "mode",
            "amount",
            "charge",
            "net_amount",
            "narration",
            "business_name",
            "created_at",
        ]
