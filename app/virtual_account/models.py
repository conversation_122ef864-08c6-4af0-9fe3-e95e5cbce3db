from common.models import AuditableModel
from django.db import models
from vas.v3.virtual_account.enums import VirtualAccountBank


class VirtualAccount(AuditableModel):
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="virtual_accounts",
    )
    bank_code = models.CharField(max_length=6)
    bank_name = models.CharField(choices=VirtualAccountBank.choices(), db_index=True)
    account_number = models.CharField(max_length=10, db_index=True)
    account_email = models.CharField(max_length=255)
    account_reference = models.Char<PERSON>ield(max_length=60)
    account_name = models.Cha<PERSON><PERSON><PERSON>(max_length=255)
    bvn = models.Char<PERSON><PERSON>(max_length=11)
    is_static = models.BooleanField(default=True)
    is_for_wallet_funding = models.BooleanField(default=False)

    class Meta:
        unique_together = ("account_number", "bank_name")

    def __str__(self):
        return f"{self.account_number} - {self.bank_name}"
