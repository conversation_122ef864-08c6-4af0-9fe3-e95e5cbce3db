import random
from datetime import datetime, timedelta
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from transaction.enums import TransactionModeEnum, TransactionStatusEnum
from transaction.models.virtual_account import VirtualAccountVasTransaction
from virtual_account.models import VirtualAccount
from wallet.enums import WalletEnums
from wallet.models import Wallet

User = get_user_model()


class Command(BaseCommand):
    help = "Seed virtual account test data for development"

    def add_arguments(self, parser):
        parser.add_argument(
            "--business-email",
            type=str,
            help="Email of the business owner to seed data for",
        )
        parser.add_argument(
            "--accounts-per-bank",
            type=int,
            default=3,
            help="Number of virtual accounts to create per bank (default: 3)",
        )
        parser.add_argument(
            "--transactions-per-account",
            type=int,
            default=5,
            help="Number of transactions to create per account (default: 5)",
        )

    def handle(self, *args, **options):
        business_email = options.get("business_email")
        accounts_per_bank = options.get("accounts_per_bank", 3)
        transactions_per_account = options.get("transactions_per_account", 5)

        self.stdout.write("🏦 SEEDING VIRTUAL ACCOUNT DATA")
        self.stdout.write("=" * 50)

        # Get business to seed data for
        if business_email:
            try:
                user = User.objects.get(email=business_email, role="Business_Owner")
                business = user.business
                self.stdout.write(
                    f"📋 Seeding data for: {business.name} ({business_email})"
                )
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(
                        f"❌ Business owner with email {business_email} not found"
                    )
                )
                return
        else:
            # Get first business owner
            try:
                user = User.objects.filter(role="Business_Owner").first()
                if not user:
                    self.stdout.write(
                        self.style.ERROR(
                            "❌ No business owners found. Create a business owner first."
                        )
                    )
                    return
                business = user.business
                self.stdout.write(
                    f"📋 Seeding data for: {business.name} ({user.email})"
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Error finding business: {str(e)}")
                )
                return

        # Bank configurations
        banks_config = [
            {
                "name": "kolomoni",
                "display_name": "KOLOMONI MFB",
                "bank_code": "090480",
                "wallet_type": WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT,
                "account_prefix": "90",
            },
            {
                "name": "access",
                "display_name": "ACCESS BANK",
                "bank_code": "044",
                "wallet_type": WalletEnums.ACCESS_VIRTUAL_ACCOUNT,
                "account_prefix": "44",
            },
            {
                "name": "wema",
                "display_name": "WEMA BANK",
                "bank_code": "035",
                "wallet_type": WalletEnums.WEMA_VIRTUAL_ACCOUNT,
                "account_prefix": "35",
            },
        ]

        # Create virtual accounts and wallets
        self.stdout.write("\n🏗️  Creating Virtual Accounts and Wallets...")

        for bank in banks_config:
            self.stdout.write(f"\n📱 {bank['display_name']}:")

            # Create wallet for this bank
            wallet, wallet_created = Wallet.objects.get_or_create(
                business=business,
                type=bank["wallet_type"],
                defaults={
                    "balance": Decimal(
                        str(random.randint(50000, 200000))
                    )  # Random balance 50k-200k
                },
            )

            if wallet_created:
                self.stdout.write(
                    f"   ✓ Created wallet with balance: ₦{wallet.balance:,.2f}"
                )
            else:
                self.stdout.write(
                    f"   ℹ️  Wallet exists with balance: ₦{wallet.balance:,.2f}"
                )

            # Create virtual accounts
            for i in range(accounts_per_bank):
                account_number = (
                    f"{bank['account_prefix']}{random.randint(********, ********)}"
                )

                va, va_created = VirtualAccount.objects.get_or_create(
                    business=business,
                    bank_name=bank["name"],
                    account_number=account_number,
                    defaults={
                        "bank_code": bank["bank_code"],
                        "account_email": f'customer{i+1}@{business.name.lower().replace(" ", "")}.com',
                        "account_reference": f'VA_{bank["name"].upper()}_{i+1}_{random.randint(1000, 9999)}',
                        "account_name": f"{business.name} Customer {i+1}",
                        "bvn": f"{random.randint(***********, ***********)}",
                        "is_static": True,
                        "is_for_wallet_funding": False,
                    },
                )

                if va_created:
                    self.stdout.write(
                        f"   ✓ Created VA: {account_number} - {va.account_name}"
                    )
                else:
                    self.stdout.write(f"   ℹ️  VA exists: {account_number}")

        # Create sample transactions
        self.stdout.write(
            f"\n💸 Creating Sample Transactions ({transactions_per_account} per account)..."
        )

        all_vas = VirtualAccount.objects.filter(business=business)
        transaction_types = [
            ("CREDIT", "Customer deposit"),
            ("CREDIT", "Transfer received"),
            ("DEBIT", "Withdrawal"),
            ("DEBIT", "Transfer sent"),
        ]

        total_transactions = 0

        for va in all_vas:
            bank_config = next(b for b in banks_config if b["name"] == va.bank_name)
            wallet = Wallet.objects.get(
                business=business, type=bank_config["wallet_type"]
            )

            for j in range(transactions_per_account):
                mode, narration_base = random.choice(transaction_types)
                amount = Decimal(str(random.randint(1000, 50000)))  # 1k-50k
                charge = Decimal(str(random.randint(10, 100)))  # 10-100 charge

                # Create transaction
                transaction = VirtualAccountVasTransaction.objects.create(
                    wallet=wallet,
                    business=business,
                    reference=f"TXN_{va.bank_name.upper()}_{random.randint(100000, 999999)}",
                    merchant_reference=f"MERCH_{random.randint(100000, 999999)}",
                    status=TransactionStatusEnum.SUCCESSFUL.value,
                    mode=getattr(TransactionModeEnum, mode).value,
                    amount=amount,
                    charge=charge,
                    net_amount=amount - charge if mode == "DEBIT" else amount + charge,
                    narration=f"{narration_base} - {va.account_name}",
                    session_id=f"SESSION_{random.randint(1000000, 9999999)}",
                    # Source details (sender)
                    source_account_number=f"{random.randint(********00, **********)}",
                    source_account_name=f"External Customer {random.randint(1, 100)}",
                    source_bank_name=random.choice(
                        ["GTB", "UBA", "ZENITH", "FIRST BANK"]
                    ),
                    source_bank_code=random.choice(["058", "033", "057", "011"]),
                    # Recipient details (VA)
                    recipient_account_number=va.account_number,
                    recipient_account_name=va.account_name,
                    recipient_bank_name=bank_config["display_name"],
                    recipient_bank_code=bank_config["bank_code"],
                )

                # Set created_at to random time in last 30 days
                days_ago = random.randint(0, 30)
                hours_ago = random.randint(0, 23)
                transaction.created_at = datetime.now() - timedelta(
                    days=days_ago, hours=hours_ago
                )
                transaction.save()

                total_transactions += 1

        # Summary
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("📊 SEEDING SUMMARY")
        self.stdout.write("=" * 50)

        for bank in banks_config:
            va_count = VirtualAccount.objects.filter(
                business=business, bank_name=bank["name"]
            ).count()
            wallet = Wallet.objects.get(business=business, type=bank["wallet_type"])
            transactions = VirtualAccountVasTransaction.objects.filter(
                business=business, recipient_bank_name__icontains=bank["name"]
            )

            inflows = transactions.filter(mode=TransactionModeEnum.CREDIT.value).count()
            outflows = transactions.filter(mode=TransactionModeEnum.DEBIT.value).count()

            self.stdout.write(f"\n🏦 {bank['display_name']}:")
            self.stdout.write(f"   Virtual Accounts: {va_count}")
            self.stdout.write(f"   Wallet Balance: ₦{wallet.balance:,.2f}")
            self.stdout.write(
                f"   Transactions: {transactions.count()} (↗️ {inflows} inflows, ↘️ {outflows} outflows)"
            )

        self.stdout.write("\n✅ Successfully seeded:")
        self.stdout.write(f"   • {all_vas.count()} virtual accounts")
        self.stdout.write("   • 3 virtual account wallets")
        self.stdout.write(f"   • {total_transactions} sample transactions")

        self.stdout.write("\n🔗 Test the filtering API:")
        self.stdout.write("   GET /api/v1/virtual-accounts/transactions/overview/")
        self.stdout.write(
            "   GET /api/v1/virtual-accounts/transactions/overview/?bank_name=kolomoni"
        )
        self.stdout.write("   GET /api/v1/virtual-accounts/transactions/bank-summary/")
        self.stdout.write(
            "   GET /api/v1/virtual-accounts/transactions/total-balance/?bank_name=access"
        )
