# Generated by Django 5.1.7 on 2025-06-22 18:31

import business.enums
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="business",
            name="owner",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="business",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="apiconfig",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="business_api_keys",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="businesschangerequest",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="change_requests",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="businesschangerequest",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="businesschangerequest",
            name="reviewed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="businessvasproduct",
            name="activated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="activated_vas_products",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="businessvasproduct",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="business.business"
            ),
        ),
        migrations.AddField(
            model_name="businessvasproduct",
            name="deactivated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="deactivated_vas_products",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="director",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="directors",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="documents",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="parent_document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="change_requests",
                to="business.document",
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="rejected_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="director",
            name="nin_document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="business.document",
            ),
        ),
        migrations.AddField(
            model_name="settlementdetail",
            name="business",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="settlement_details",
                to="business.business",
            ),
        ),
        migrations.AddField(
            model_name="socialmedia",
            name="business",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="social_media_channels",
                to="business.business",
            ),
        ),
        migrations.AddConstraint(
            model_name="businesschangerequest",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("status", business.enums.ChangeRequestStatus["Pending"])
                ),
                fields=("business", "section"),
                name="unique_pending_request_per_section",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="businessvasproduct",
            unique_together={("business", "product_type")},
        ),
        migrations.AlterUniqueTogether(
            name="socialmedia",
            unique_together={("business", "channel")},
        ),
    ]
