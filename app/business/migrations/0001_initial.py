# Generated by Django 5.1.7 on 2025-06-22 18:31

import business.enums
import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="APIConfig",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("private_key", models.CharField(blank=True, null=True)),
                ("public_key", models.CharField(blank=True, db_index=True, null=True)),
                ("webhook_url", models.URLField(blank=True, null=True)),
                ("webhook_signature", models.TextField(blank=True, null=True)),
                ("whitelisted_ips", models.CharField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Business",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        db_index=True,
                        max_length=254,
                        null=True,
                        unique=True,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "phone",
                    models.CharField(blank=True, max_length=20, null=True, unique=True),
                ),
                ("website", models.URLField(blank=True, null=True, unique=True)),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Inactive", "Inactive"),
                            ("Verified", "Verified"),
                            ("Active", "Active"),
                        ],
                        default="Inactive",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if the business has been verified by an admin.",
                    ),
                ),
                (
                    "office_address",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("street", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=50, null=True)),
                ("state", models.CharField(blank=True, max_length=50, null=True)),
                ("postal_code", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "rc_number",
                    models.CharField(blank=True, max_length=20, null=True, unique=True),
                ),
                (
                    "onboarding_stage",
                    models.CharField(
                        blank=True,
                        choices=business.enums.OnboardingStage.choices,
                        default="BusinessInformation",
                        max_length=40,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Businesses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BusinessChangeRequest",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "section",
                    models.CharField(
                        choices=[
                            ("BusinessInformation", "Businessinformation"),
                            ("DirectorsAndOwners", "Directorsandowners"),
                            ("SettlementDetails", "Settlementdetails"),
                            (
                                "Documentation_Proof_Of_Address",
                                "Documentationproofofaddress",
                            ),
                            (
                                "Documentation_Certificate_Of_Incorporation",
                                "Documentationcertificateofincorporation",
                            ),
                            (
                                "Documentation_Memorandum_Of_Association",
                                "Documentationmemorandumofassociation",
                            ),
                        ],
                        max_length=100,
                    ),
                ),
                ("object_id", models.CharField(blank=True, null=True)),
                ("old_value", models.JSONField()),
                ("new_value", models.JSONField()),
                (
                    "change_type",
                    models.CharField(
                        choices=[
                            ("Creation", "Creation"),
                            ("Modification", "Modification"),
                        ],
                        default="Modification",
                        max_length=20,
                    ),
                ),
                ("rejection_note", models.TextField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("Approved", "Approved"),
                            ("Rejected", "Rejected"),
                        ],
                        default=business.enums.ChangeRequestStatus["Pending"],
                        max_length=30,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name_plural": "Business Change Requests",
            },
        ),
        migrations.CreateModel(
            name="BusinessVASProduct",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "product_type",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("activated_at", models.DateTimeField(auto_now_add=True)),
                ("deactivated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name_plural": "Business VAS Products",
            },
        ),
        migrations.CreateModel(
            name="Director",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(db_index=True, max_length=250)),
                ("email", models.EmailField(db_index=True, max_length=254)),
                ("phone", models.CharField(max_length=20)),
                ("bvn", models.CharField(max_length=20)),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("document", models.FileField(upload_to="documents")),
                ("document_name", models.CharField(db_index=True, max_length=250)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("Approved", "Approved"),
                            ("Rejected", "Rejected"),
                            ("ChangeRequested", "ChangeRequested"),
                        ],
                        default="Pending",
                        max_length=100,
                    ),
                ),
                (
                    "rejection_message",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("rejected_at", models.DateTimeField(blank=True, null=True)),
                ("resubmitted_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="SettlementDetail",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("bank_name", models.CharField(max_length=50)),
                ("bank_code", models.CharField(max_length=10)),
                ("account_number", models.CharField(db_index=True, max_length=10)),
                ("account_name", models.CharField(max_length=100, null=True)),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="SocialMedia",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("Facebook", "Facebook"),
                            ("Twitter", "Twitter"),
                            ("Instagram", "Instagram"),
                            ("LinkedIn", "LinkedIn"),
                        ],
                        max_length=50,
                    ),
                ),
                ("url", models.URLField(max_length=255)),
            ],
            options={
                "verbose_name_plural": "Social Media Channels",
                "ordering": ("-created_at",),
            },
        ),
    ]
