from business.models import Business, BusinessVASProduct
from django.utils import timezone
from transaction.enums import TransactionClassEnum
from user.models import User
from wallet.enums import AdminActivatedWalletType, WalletEnums
from wallet.models import Wallet
from wallet.v1.serializers import WalletActivationSerializer


class BaseVASProductHandler:
    product_type: str = None  # To be set in subclass

    def activate(self, business: "Business", admin: "User", notes=None):
        vas, _ = BusinessVASProduct.objects.update_or_create(
            business=business,
            product_type=self.product_type,
            defaults={
                "is_active": True,
                "activated_by": admin,
                "notes": notes,
                "deactivated_at": None,
            },
        )
        return vas

    def deactivate(self, business: "Business", admin: "User", notes=None):
        vas = BusinessVASProduct.objects.filter(
            business=business, product_type=self.product_type
        ).first()
        if not vas:
            return None
        vas.is_active = False
        vas.deactivated_at = timezone.now()
        vas.deactivated_by = admin
        vas.notes = notes
        vas.save()
        return vas


class AirtimeProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.AIRTIME.value

    def activate(self, business: Business, admin: User, notes=None):
        return super().activate(business, admin, notes)


class BettingProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.BETTING.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class CableTVProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.CABLE_TV.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class DataProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.DATA.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class EducationProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.EDUCATION.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class ElectricityProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.ELECTRICITY.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class EPINProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.EPIN.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class KYCProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.KYC.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class TransferProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.TRANSFER.value

    def activate(self, business: Business, admin: User, notes=None):
        super().activate(business, admin, notes)


class RecurringDebitProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.RECURRING_DEBIT

    def activate(self, business: Business, admin: User, notes=None):
        if not Wallet.objects.filter(
            business=business, type=AdminActivatedWalletType.RECURRING_DEBIT
        ).exists():
            data = {
                "business": business.id,
                "wallet_type": AdminActivatedWalletType.RECURRING_DEBIT,
            }
            serializer = WalletActivationSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            serializer.save()

        super().activate(business, admin, notes)


class VirtualAccountProductHandler(BaseVASProductHandler):
    product_type = TransactionClassEnum.VIRTUAL_ACCOUNT.value

    def activate(self, business: Business, admin: User, notes=None):
        for wallet_type in WalletEnums().va_wallets():
            if not Wallet.objects.filter(business=business, type=wallet_type).exists():
                data = {
                    "business": business.id,
                    "wallet_type": wallet_type,
                }
                serializer = WalletActivationSerializer(data=data)
                serializer.is_valid(raise_exception=True)
                serializer.save()

        return super().activate(business, admin, notes)


class VASProductHandlerFactory:
    handlers = {
        TransactionClassEnum.RECURRING_DEBIT.value: RecurringDebitProductHandler(),
        TransactionClassEnum.AIRTIME.value: AirtimeProductHandler(),
        TransactionClassEnum.BETTING.value: BettingProductHandler(),
        TransactionClassEnum.CABLE_TV.value: CableTVProductHandler(),
        TransactionClassEnum.DATA.value: DataProductHandler(),
        TransactionClassEnum.EDUCATION.value: EducationProductHandler(),
        TransactionClassEnum.ELECTRICITY.value: ElectricityProductHandler(),
        TransactionClassEnum.EPIN.value: EPINProductHandler(),
        TransactionClassEnum.KYC.value: KYCProductHandler(),
        TransactionClassEnum.TRANSFER.value: TransferProductHandler(),
        TransactionClassEnum.VIRTUAL_ACCOUNT.value: VirtualAccountProductHandler(),
    }

    @classmethod
    def get_handler(cls, product_type: str):
        return cls.handlers.get(product_type, BaseVASProductHandler())
