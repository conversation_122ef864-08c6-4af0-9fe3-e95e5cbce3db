from business.models import APIConfig, Business
from django.contrib.auth.hashers import make_password


class APIConfigHandler:
    @staticmethod
    def save_pub_key(business: Business):

        public_key, _ = APIConfig().generate_keys()
        api_config, _ = APIConfig.objects.get_or_create(
            business=business, defaults={"public_key": public_key}
        )

        return api_config.public_key

    @staticmethod
    def save_private_key(business: Business):
        _, private_key = APIConfig().generate_keys()
        APIConfig.objects.update_or_create(
            business=business, defaults={"private_key": make_password(private_key)}
        )

        return private_key

    @staticmethod
    def get_public_key(business: Business):
        api_config = APIConfig.objects.filter(business=business).first()
        if not api_config:
            return None

        return api_config.public_key
