from unittest.mock import MagicMock, patch

from business.enums import OnboardingStage
from business.tests.fixtures import (
    create_business,
    create_test_user,
    get_valid_business_data,
)
from business.v1.serializers import BusinessInformationSerializer
from django.test import TestCase
from pykolofinance.common.helpers import clean_phone_number


class BusinessInformationSerializerTest(TestCase):

    def setUp(self):
        self.user = create_test_user(email="<EMAIL>")
        self.business = create_business(
            owner=self.user,
            description=None,
            email="<EMAIL>",
            website="https://test-sage-1.com",
        )

        # Use fixture for valid data
        self.valid_data = get_valid_business_data()

        # Create invalid email data based on valid data
        self.invalid_email_data = {**self.valid_data, "email": "invalid-email"}

        # Create business with duplicate email
        other_user = create_test_user(email="<EMAIL>")
        self.duplicate_email_business = create_business(
            name="Sage Business",
            owner=other_user,
            email="<EMAIL>",
            phone="***********",
            rc_number="RC789012",
            website="https://test-sage-2.com",
        )

        another_user = create_test_user(email="<EMAIL>")
        self.duplicate_phone_business = create_business(
            name="Duplicate Phone Business",
            owner=another_user,
            email="<EMAIL>",
            phone="***********",
            rc_number="RC555555",
        )

        # Create data with duplicate email and phone
        self.duplicate_email_data = {**self.valid_data, "email": "<EMAIL>"}

        self.duplicate_phone_data = {**self.valid_data, "phone": "***********"}

    def test_valid_data_serialization(self):
        """Test serializer with valid data"""
        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.valid_data
        )
        self.assertTrue(serializer.is_valid())

    def test_invalid_email(self):
        """Test serializer with invalid email"""
        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.invalid_email_data
        )
        self.assertFalse(serializer.is_valid())
        self.assertIn("email", serializer.errors)

    def test_duplicate_email(self):
        """Test serializer with duplicate email"""
        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.duplicate_email_data
        )
        self.assertFalse(serializer.is_valid())
        self.assertIn("email", serializer.errors)
        self.assertEqual(
            serializer.errors["email"][0], "business with this email already exists."
        )

    def test_duplicate_phone(self):
        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.duplicate_phone_data
        )
        self.assertFalse(serializer.is_valid())
        self.assertIn("phone", serializer.errors)
        self.assertEqual(
            serializer.errors["phone"][0], "business with this phone already exists."
        )

    def test_update_method(self):
        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.valid_data
        )
        self.assertTrue(serializer.is_valid())
        updated_business = serializer.save()

        self.assertEqual(updated_business.email, self.valid_data["email"])
        self.assertEqual(updated_business.description, self.valid_data["description"])
        self.assertEqual(
            updated_business.phone, clean_phone_number(self.valid_data["phone"])
        )
        self.assertEqual(updated_business.rc_number, self.valid_data["rc_number"])
        self.assertEqual(updated_business.street, self.valid_data["street"])
        self.assertEqual(updated_business.city, self.valid_data["city"])
        self.assertEqual(updated_business.state, self.valid_data["state"])
        self.assertEqual(
            updated_business.office_address, self.valid_data["office_address"]
        )
        self.assertEqual(updated_business.postal_code, self.valid_data["postal_code"])
        self.assertEqual(updated_business.website, self.valid_data["website"])

        self.assertEqual(
            updated_business.onboarding_stage, OnboardingStage.Documentation.value
        )

    @patch("business.v1.serializers.clean_phone_number")
    def test_phone_number_cleaning_called(self, mock_clean_phone):
        mock_clean_phone.return_value = "+2341234567890"

        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.valid_data
        )
        serializer.is_valid()

        mock_clean_phone.assert_called_once_with(self.valid_data["phone"].strip())

    @patch("business.v1.serializers.validate_email")
    def test_email_validation_called(self, mock_validate_email):
        """Test that validate_email is called with the correct arguments"""
        mock_email = MagicMock()
        mock_email.normalized = "<EMAIL>"
        mock_validate_email.return_value = mock_email

        serializer = BusinessInformationSerializer(
            instance=self.business, data=self.valid_data
        )
        serializer.is_valid()

        # Check if validate_email was called with the correct argument
        mock_validate_email.assert_called_once_with(self.valid_data["email"])

        # Save and verify normalized email was used
        updated_business = serializer.save()
        self.assertEqual(updated_business.email, "<EMAIL>")
