from business.enums import OnboardingStage
from business.models import Business
from business.tests.fixtures import create_business, create_test_user
from django.test import TestCase


class BusinessModelTest(TestCase):

    def setUp(self):
        self.user = create_test_user()
        self.business = create_business(
            owner=self.user, office_address="123 Main St", street="Business District"
        )

    def test_str_method(self):
        self.assertEqual(str(self.business), "Test Business")

    def test_full_address_property(self):
        expected_address = (
            "123 Main St, Business District, Test City, Test State, 12345"
        )
        self.assertEqual(self.business.full_address, expected_address)

    def test_default_onboarding_stage(self):
        new_user = create_test_user(email="<EMAIL>")
        new_business = Business.objects.create(name="New Business", owner=new_user)
        self.assertEqual(
            new_business.onboarding_stage, OnboardingStage.BusinessInformation.value
        )
