"""
Common fixtures for business tests.
This module contains fixtures that can be imported and reused across all business tests.
"""

from business.enums import OnboardingStage
from business.models import Business
from django.contrib.auth import get_user_model

User = get_user_model()


def create_test_user(
    email="<EMAIL>",
    password="sage-dev-pass",
    firstname="sage",
    lastname="dev",
):
    return User.objects.create_user(
        email=email,
        password=password,
        firstname=firstname,
        lastname=lastname,
        role="Business_Owner",
    )


def create_business(
    name="Test Business",
    owner=None,
    email="<EMAIL>",
    phone="***********",
    rc_number="RC123456",
    description="Test description",
    street="Test Street",
    city="Test City",
    state="Test State",
    office_address="Test Office Address",
    postal_code="12345",
    website="https://test1website.com",
    onboarding_stage=OnboardingStage.BusinessInformation.value,
):
    """
    Create a test business.

    Args:
        name: The name of the business
        owner: The owner user of the business
        email: The business email
        phone: The business phone number
        rc_number: The business registration number
        description: The business description
        street: The business street address
        city: The business city
        state: The business state
        office_address: The business office address
        postal_code: The business postal code
        website: The business website
        onboarding_stage: The current onboarding stage

    Returns:
        Business: The created business instance
    """
    if owner is None:
        owner = create_test_user()

    return Business.objects.create(
        name=name,
        owner=owner,
        email=email,
        phone=phone,
        rc_number=rc_number,
        description=description,
        street=street,
        city=city,
        state=state,
        office_address=office_address,
        postal_code=postal_code,
        website=website,
        onboarding_stage=onboarding_stage,
    )


def get_valid_business_data(
    email="<EMAIL>",
    description="Just selling",
    phone="***********",
    rc_number="RC654321",
    street="123 Test St",
    city="Test City",
    state="Lagos",
    office_address="Test Office Address",
    website="https://test2website.com",
    postal_code="12345",
):
    """
    Get valid business data for testing.

    Args:
        email: The business email
        description: The business description
        phone: The business phone number
        rc_number: The business registration number
        street: The business street address
        city: The business city
        state: The business state
        office_address: The business office address
        website: The business website
        postal_code: The business postal code

    Returns:
        dict: A dictionary of valid business data
    """
    return {
        "email": email,
        "description": description,
        "phone": phone,
        "rc_number": rc_number,
        "street": street,
        "city": city,
        "state": state,
        "office_address": office_address,
        "website": website,
        "postal_code": postal_code,
    }
