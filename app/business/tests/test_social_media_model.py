import pytest
from business.enums import SocialMediaChannel
from business.models import SocialMedia
from business.tests.fixtures import create_business, create_test_user
from django.db import IntegrityError


@pytest.mark.django_db
class TestSocialMediaModel:
    """Test cases for the SocialMedia model"""

    def test_create_social_media(self):
        """Test creating a social media channel"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        social_media = SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Facebook.value,
            url="https://facebook.com/mybusiness",
        )

        assert social_media.id is not None
        assert social_media.business == business
        assert social_media.channel == SocialMediaChannel.Facebook.value
        assert social_media.url == "https://facebook.com/mybusiness"

    def test_str_method(self):
        """Test the __str__ method"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        social_media = SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Twitter.value,
            url="https://twitter.com/mybusiness",
        )

        expected_str = f"{business.name} - {SocialMediaChannel.Twitter.value}"
        assert str(social_media) == expected_str

    def test_unique_together_constraint(self):
        """Test that a business cannot have duplicate channels"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        # Create a social media channel
        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Instagram.value,
            url="https://instagram.com/mybusiness",
        )

        # Try to create another with the same channel
        with pytest.raises(IntegrityError):
            SocialMedia.objects.create(
                business=business,
                channel=SocialMediaChannel.Instagram.value,
                url="https://instagram.com/mybusiness-duplicate",
            )

    def test_multiple_channels_for_business(self):
        """Test that a business can have multiple different channels"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        # Create multiple social media channels
        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Facebook.value,
            url="https://facebook.com/mybusiness",
        )

        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Twitter.value,
            url="https://twitter.com/mybusiness",
        )

        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.Instagram.value,
            url="https://instagram.com/mybusiness",
        )

        SocialMedia.objects.create(
            business=business,
            channel=SocialMediaChannel.LinkedIn.value,
            url="https://linkedin.com/company/mybusiness",
        )

        assert SocialMedia.objects.filter(business=business).count() == 4
        channels = SocialMedia.objects.filter(business=business).values_list(
            "channel", flat=True
        )
        assert SocialMediaChannel.Facebook.value in channels
        assert SocialMediaChannel.Twitter.value in channels
        assert SocialMediaChannel.Instagram.value in channels
        assert SocialMediaChannel.LinkedIn.value in channels
