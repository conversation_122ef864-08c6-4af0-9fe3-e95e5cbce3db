from business.v1.views import (
    APIConfigViewSet,
    BusinessChangeRequestViewSet,
    OnboardBusinessViewSet,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter

app_name = "business"

router = DefaultRouter()
router.register("api-configs", APIConfigViewSet, basename="api-config")
router.register(
    "change-requests", BusinessChangeRequestViewSet, basename="change-request"
)
router.register("", OnboardBusinessViewSet, basename="onboard-business")

urlpatterns = [
    path("", include(router.urls)),
]
