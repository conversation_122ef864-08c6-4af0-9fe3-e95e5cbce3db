from common.models import AuditableModel
from django.db import models
from teams.enums import (
    SystemFeature,
    SystemPermission,
    SystemPlatform,
    SystemRoleType,
    TeamMemberStatus,
)


class Permission(AuditableModel):
    codename = models.CharField(max_length=100, unique=True)
    feature = models.CharField(
        max_length=100,
        choices=SystemFeature.choices(),
        help_text="Feature this permission is associated with",
        db_index=True,
    )
    action = models.CharField(max_length=50, choices=SystemPermission.choices())

    def __str__(self):
        return self.codename


class Role(AuditableModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    type = models.CharField(
        max_length=20,
        choices=SystemRoleType.choices(),
        default=SystemRoleType.Default.value,
        help_text="Type of role (e.g., custom, default)",
        db_index=True,
    )
    platform = models.CharField(
        max_length=20,
        choices=SystemPlatform.choices(),
        help_text="Platform for which this role is applicable (e.g., admin, business, global)",
        db_index=True,
    )
    permissions = models.ManyToManyField(Permission, related_name="roles")
    business = models.ForeignKey(
        "business.Business",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="roles",
        db_index=True,
    )

    class Meta:
        unique_together = ("name", "platform")
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def __str__(self):
        return self.name


class TeamMember(AuditableModel):
    user = models.OneToOneField(
        "user.User", on_delete=models.CASCADE, related_name="teammember"
    )
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.CASCADE,
        related_name="core_team_members",
        db_index=True,
    )
    role = models.ForeignKey(
        Role, on_delete=models.PROTECT, related_name="team_members"
    )
    status = models.CharField(
        max_length=20,
        choices=TeamMemberStatus.choices(),
        default=TeamMemberStatus.Invited.value,
        db_index=True,
    )
    invited_by = models.ForeignKey(
        "user.User",
        null=True,
        on_delete=models.SET_NULL,
        related_name="invited_team_members",
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Team Member"
        verbose_name_plural = "Team Members"

    def __str__(self):
        return f"{self.user.email} -- {self.role.name}"


class AdminProfile(AuditableModel):
    user = models.OneToOneField(
        "user.User", on_delete=models.CASCADE, related_name="admin_profile"
    )
    role = models.ForeignKey(Role, on_delete=models.PROTECT, related_name="admin_users")

    class Meta:
        verbose_name = "Admin Profile"
        verbose_name_plural = "Admin Profiles"

    def __str__(self):
        return f"{self.user.email} -- {self.role.name}"
