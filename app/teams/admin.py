from django.contrib import admin

from .models import AdminProfile, Permission, Role, TeamMember


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ("user", "business", "role", "status", "joined_at")
    search_fields = ("user__email", "business__name", "role__name")
    list_filter = ("status", "role", "business")


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ("name", "platform")
    search_fields = ("name", "description")
    filter_horizontal = ("permissions",)


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ("codename", "feature", "action")
    search_fields = ("codename", "feature", "action")


@admin.register(AdminProfile)
class AdminProfileAdmin(admin.ModelAdmin):
    list_display = ("user", "role")
    search_fields = ("user__email", "role__name")
    list_filter = ("role",)
