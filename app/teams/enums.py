from common.enums import CustomEnum


class SystemFeature(CustomEnum):
    # DASHBOARD
    DashboardMetrics = "dashboard_metrics"

    # PRODUCTS
    Products = "products"

    # TRANSACTIONS
    Transactions = "transactions"

    # VIRTUAL ACCOUNTS
    VirtualAccounts = "virtual_accounts"
    VirtualAccountsWithdrawal = "virtual_accounts_withdrawal"

    # SETTINGS
    ChangeRequest = "change_request"
    Teams = "teams"
    Developer = "developer"
    Security = "security"

    # RECURRING DEBITS
    RecurrentDebitTransaction = "recurrent_debit_transaction"
    RecurrentDebitMandate = "recurrent_debit_mandate"
    RecurrentDebitWallet = "recurrent_debit_wallet"

    # DISPUTE
    Dispute = "dispute"

    # AUDIT
    Audit = "audit_logs"


class SystemRoleType(CustomEnum):
    Default = "DEFAULT"
    Custom = "CUSTOM"


class SystemPermission(CustomEnum):
    Create = "CREATE"
    Read = "READ"
    Update = "UPDATE"


class SystemPlatform(CustomEnum):
    Admin = "Admin"
    Business = "Business"


class SystemBaseUserRole(CustomEnum):
    Admin = "Admin"
    BusinessOwner = "Business_Owner"
    TeamMember = "Team_Member"


class TeamMemberStatus(CustomEnum):
    Invited = "Invited"
    Active = "Active"
    Revoked = "Revoked"


class DefaultBusinessRole(CustomEnum):
    CustomerSupport = "Customer_Support"
    Operations = "Operations"
    Reconciliation = "Reconciliation"
    Developer = "Developer"
    Admin = "Admin"
    BusinessOwner = "Business_Owner"


class DefaultAdminRole(CustomEnum):
    CustomerSupport = "Customer_Support"
    Operations = "Operations"
    Reconciliation = "Reconciliation"
    Admin = "Admin"
    SuperAdmin = "Super_Admin"
