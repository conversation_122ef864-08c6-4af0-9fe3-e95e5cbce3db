from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsBusinessOwner, IsPlatformOwner
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.enums import SystemBaseUserRole, SystemPlatform, SystemRoleType
from teams.models import Role, TeamMember
from teams.v1.serializers import (
    RoleSerializer,
    TeamMemberInviteSerializer,
    TeamMemberReinviteSerializer,
    TeamMemberRevokeSerializer,
    TeamMemberSerializer,
)


class RoleViewSet(viewsets.ModelViewSet):
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated, IsPlatformOwner]
    pagination_class = LargeDatasetKeySetPagination

    def get_queryset(self):

        platform = (
            SystemPlatform.Business.value
            if self.request.user.role == SystemBaseUserRole.BusinessOwner.value
            else SystemPlatform.Admin.value
        )
        return Role.objects.filter(platform=platform)

    def perform_create(self, serializer):
        user = self.request.user
        if user.role == SystemBaseUserRole.BusinessOwner.value:
            platform = SystemPlatform.Business.value
            business = getattr(user, "business", None)
            serializer.save(
                platform=platform, business=business, type=SystemRoleType.Custom.value
            )
        else:
            platform = SystemPlatform.Admin.value
            serializer.save(platform=platform, type=SystemRoleType.Custom.value)

    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_object())
        serializer.delete()
        return Response({"message": "Role deleted"}, status=status.HTTP_200_OK)


class BusinessTeamMemberViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsBusinessOwner]
    pagination_class = LargeDatasetKeySetPagination
    queryset = TeamMember.objects.select_related("user", "role", "invited_by")

    def get_queryset(self):
        return self.queryset.filter(business=self.request.user.business)

    def get_serializer_class(self):
        if self.action == "create":
            return TeamMemberInviteSerializer
        if self.action == "reinvite":
            return TeamMemberReinviteSerializer
        if self.action == "revoke":
            return TeamMemberRevokeSerializer
        return TeamMemberSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context.update(
            {
                "platform": SystemPlatform.Business.value,
                "business": self.request.user.business,
            }
        )
        return context

    def perform_create(self, serializer):
        serializer.save()

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def reinvite(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"detail": "Invitation resent."})

    @action(detail=True, methods=["post"])
    def revoke(self, request, pk=None):
        instance = self.get_object()
        serializer = self.get_serializer(instance=instance, data={})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({"detail": "Access revoked successfully."})
