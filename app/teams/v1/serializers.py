from rest_framework import serializers
from teams.enums import TeamMemberStatus
from teams.models import Permission, Role, SystemPlatform, SystemRoleType, TeamMember
from user.models import User


class RoleSerializer(serializers.ModelSerializer):
    permissions = serializers.ListField(child=serializers.CharField(), write_only=True)
    permissions_list = serializers.SerializerMethodField(read_only=True)
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "description",
            "platform",
            "type",
            "permissions",
            "permissions_list",
            "user_count",
        ]
        read_only_fields = ["platform", "type", "user_count", "permissions_list"]

    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user
        platform = (
            SystemPlatform.Business.value
            if user.role == "Business_Owner"
            else SystemPlatform.Admin.value
        )
        business = getattr(user, "business", None)

        name = attrs.get("name")
        instance = getattr(self, "instance", None)

        query = Role.objects.filter(name=name, platform=platform)
        if platform == SystemPlatform.Business.value:
            query = query.filter(business=business)
        else:
            query = query.filter(business__isnull=True)

        if instance:
            query = query.exclude(pk=instance.pk)

        if query.exists():
            raise serializers.ValidationError(
                {
                    "name": f"A role with the name '{name}' already exists for this platform."
                }
            )

        return attrs

    def validate_permissions(self, value):
        if not value:
            raise serializers.ValidationError("At least one permission is required.")

        valid_permissions = Permission.objects.filter(codename__in=value)
        if valid_permissions.count() != len(set(value)):
            existing = set(valid_permissions.values_list("codename", flat=True))
            missing = set(value) - existing
            raise serializers.ValidationError(
                f"Invalid permission codenames: {', '.join(missing)}"
            )

        return value

    def create(self, validated_data):
        permission_codenames = validated_data.pop("permissions")
        role = Role.objects.create(**validated_data)
        role.permissions.set(
            Permission.objects.filter(codename__in=permission_codenames)
        )
        return role

    def update(self, instance, validated_data):
        if instance.type == SystemRoleType.Default.value:
            raise serializers.ValidationError("Default roles cannot be edited.")

        permission_codenames = validated_data.pop("permissions", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if permission_codenames is not None:
            instance.permissions.set(
                Permission.objects.filter(codename__in=permission_codenames)
            )
        return instance

    def delete(self):
        if self.instance.type == SystemRoleType.Default.value:
            raise serializers.ValidationError("Cannot delete a default role.")
        if self.instance.team_members.exists():
            raise serializers.ValidationError(
                "Cannot delete a role assigned to team members."
            )
        self.instance.delete()

    def get_permissions_list(self, obj):
        return list(
            obj.permissions.order_by("codename").values_list("codename", flat=True)
        )

    def get_user_count(self, obj):
        return obj.team_members.count()


class TeamMemberInviteSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    phone_number = serializers.CharField()
    email = serializers.EmailField()
    role_id = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all())

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_role_id(self, role):
        platform = self.context.get("platform")
        business = self.context.get("business")

        if platform != SystemPlatform.Business.value:
            raise serializers.ValidationError("Invalid platform context.")

        if role.platform != SystemPlatform.Business.value or role.business != business:
            raise serializers.ValidationError("Invalid role for this business.")

        return role

    def create(self, validated_data):
        email = validated_data["email"]
        user = User.objects.create_user(
            email=email,
            firstname=validated_data["first_name"],
            lastname=validated_data["last_name"],
            phone=validated_data["phone_number"],
            is_active=False,
        )

        team_member = TeamMember.objects.create(
            user=user,
            role=validated_data["role_id"],
            business=self.context["business"],
            invited_by=self.context["request"].user,
            status=TeamMemberStatus.Invited.value,
        )

        # send_invite_email(user.email, first_name=user.firstname) #TODO: Implement email sending logic
        return team_member


class TeamMemberSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source="role.name", read_only=True)
    email = serializers.EmailField(source="user.email", read_only=True)
    first_name = serializers.CharField(source="user.firstname", read_only=True)
    last_name = serializers.CharField(source="user.lastname", read_only=True)
    phone_number = serializers.CharField(source="user.phone", read_only=True)

    class Meta:
        model = TeamMember
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "phone_number",
            "role_name",
            "status",
            "joined_at",
        ]


class TeamMemberReinviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def save(self, **kwargs):
        member = self.instance
        if member.status == TeamMemberStatus.Revoked.value:
            raise serializers.ValidationError("Cannot reinvite a revoked team member.")

        # send_invite_email(member.user.email, first_name=member.user.firstname) #TODO: Implement email sending logic
        return member


class TeamMemberRevokeSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamMember
        fields = []

    def save(self, **kwargs):
        member = self.instance
        member.status = TeamMemberStatus.Revoked.value
        member.user.is_active = False
        member.user.save()
        member.save()
        return member
