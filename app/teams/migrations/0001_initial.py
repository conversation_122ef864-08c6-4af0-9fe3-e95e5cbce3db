# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AdminProfile",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Admin Profile",
                "verbose_name_plural": "Admin Profiles",
            },
        ),
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("codename", models.CharField(max_length=100, unique=True)),
                (
                    "feature",
                    models.CharField(
                        choices=[
                            ("dashboard_metrics", "dashboard_metrics"),
                            ("products", "products"),
                            ("transactions", "transactions"),
                            ("virtual_accounts", "virtual_accounts"),
                            (
                                "virtual_accounts_withdrawal",
                                "virtual_accounts_withdrawal",
                            ),
                            ("change_request", "change_request"),
                            ("teams", "teams"),
                            ("developer", "developer"),
                            ("security", "security"),
                            (
                                "recurrent_debit_transaction",
                                "recurrent_debit_transaction",
                            ),
                            ("recurrent_debit_mandate", "recurrent_debit_mandate"),
                            ("recurrent_debit_wallet", "recurrent_debit_wallet"),
                            ("dispute", "dispute"),
                            ("audit_logs", "audit_logs"),
                        ],
                        db_index=True,
                        help_text="Feature this permission is associated with",
                        max_length=100,
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("CREATE", "CREATE"),
                            ("READ", "READ"),
                            ("UPDATE", "UPDATE"),
                        ],
                        max_length=50,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "type",
                    models.CharField(
                        choices=[("DEFAULT", "DEFAULT"), ("CUSTOM", "CUSTOM")],
                        db_index=True,
                        default="DEFAULT",
                        help_text="Type of role (e.g., custom, default)",
                        max_length=20,
                    ),
                ),
                (
                    "platform",
                    models.CharField(
                        choices=[("Admin", "Admin"), ("Business", "Business")],
                        db_index=True,
                        help_text="Platform for which this role is applicable (e.g., admin, business, global)",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "verbose_name": "Role",
                "verbose_name_plural": "Roles",
            },
        ),
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Invited", "Invited"),
                            ("Active", "Active"),
                            ("Revoked", "Revoked"),
                        ],
                        db_index=True,
                        default="Invited",
                        max_length=20,
                    ),
                ),
                ("joined_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Team Member",
                "verbose_name_plural": "Team Members",
            },
        ),
    ]
