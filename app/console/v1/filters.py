import django_filters
from business.models import BusinessChangeRequest
from common.filter import DateFilter


class AdminBusinessChangeRequestFilter(DateFilter):
    business_name = django_filters.CharFilter(
        field_name="business__name", lookup_expr="icontains"
    )
    business_email = django_filters.CharFilter(
        field_name="business__email", lookup_expr="icontains"
    )
    business_phone = django_filters.CharFilter(
        field_name="business__phone", lookup_expr="icontains"
    )

    class Meta:
        model = BusinessChangeRequest
        fields = [
            "business_name",
            "business_email",
            "business_phone",
            "status",
            "section",
            "change_type",
        ]
