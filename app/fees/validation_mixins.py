from abc import abstractmethod

from fees.enums import BandedFeeType, FeeType
from rest_framework import serializers


def validate_bounded_configurations(data):
    if not data or not isinstance(data, list):
        raise serializers.ValidationError("Expected a list of configuration objects.")

    data = sorted(data, key=lambda x: x["lower_bound"])

    # First lower_bound must be 0
    if data[0]["lower_bound"] != 0:
        raise serializers.ValidationError(
            {"lower_bound": "The first configuration must start at lower_bound = 0."}
        )

    for i in range(len(data)):
        current = data[i]
        fee_type = current.get("fee_type", BandedFeeType.Fixed.value)
        amount = current.get("amount", 0)

        if amount < 0:
            raise serializers.ValidationError(
                {"amount": "Banded Amount for banded charges must be positive."}
            )

        if fee_type in [FeeType.Percentage.value, FeeType.Fixed.value]:

            if fee_type == FeeType.Percentage.value and not amount < 100:
                raise serializers.ValidationError(
                    {"amount": "Banded Amount must be between 0 and 100"}
                )

        # Check lower_bound < upper_bound
        if current["lower_bound"] >= current["upper_bound"] and not current.get(
            "upper_bound_infinite", False
        ):
            raise serializers.ValidationError(
                {
                    "upper_bound": (
                        f"Upper bound must be greater than lower bound for range "
                        f"{current['lower_bound']} - {current['upper_bound']}"
                    )
                }
            )

        # Check overlap with previous config
        if i > 0:
            previous = data[i - 1]
            if current["lower_bound"] <= previous["upper_bound"] and not current.get(
                "upper_bound_infinite", False
            ):
                raise serializers.ValidationError(
                    {
                        "lower_bound": (
                            f"Range {current['lower_bound']} - {current['upper_bound']} "
                            f"overlaps with {previous['lower_bound']} - {previous['upper_bound']}."
                        )
                    }
                )

            # Enforce continuity
            if current["lower_bound"] != previous["upper_bound"] + 1:
                raise serializers.ValidationError(
                    {
                        "lower_bound": (
                            f"Configuration with lower_bound = {current['lower_bound']} must follow "
                            f"immediately after upper_bound = {previous['upper_bound']}."
                        )
                    }
                )

        # Only allow upper_bound_infinite on last config
        if i < len(data) - 1 and current.get("upper_bound_infinite", False):
            raise serializers.ValidationError(
                {
                    "upper_bound_infinite": "Only the last configuration can have an infinite upper bound."
                }
            )

    # Final config upper_bound_infinite check
    last_config = data[-1]
    if last_config["upper_bound"] == 0:
        if not last_config.get("upper_bound_infinite", False):
            raise serializers.ValidationError(
                {
                    "upper_bound_infinite": (
                        "The last configuration must have 'upper_bound_infinite' = true if upper_bound = 0."
                    )
                }
            )
    else:
        if last_config.get("upper_bound_infinite", False):
            raise serializers.ValidationError(
                {
                    "upper_bound_infinite": (
                        "'upper_bound_infinite' must be false if upper_bound is not 0."
                    )
                }
            )


class FeeValidationMixin:
    def validate(self, attrs):
        data = super().validate(attrs)
        fee_type = data.get("fee_type")
        amount = data.get("amount", 0)
        band_category = self._band_category()

        if fee_type == FeeType.Banded.value:

            if data.get(band_category, None) is None:
                raise serializers.ValidationError(
                    {f"{band_category}": f"{band_category} field is required"}
                )

            validate_bounded_configurations(data.get(band_category, []))

        if fee_type in [FeeType.Percentage.value, FeeType.Fixed.value]:

            if amount < 0:
                raise serializers.ValidationError(
                    {"amount": "Amount cannot be negative"}
                )

            if fee_type == FeeType.Percentage.value and not amount < 100:
                raise serializers.ValidationError(
                    {"amount": "Amount must be between 0 and 100"}
                )

        if data.get("cap_amount", 0) < 0:
            raise serializers.ValidationError(
                {"cap_amount": "Cap Amount cannot be negative"}
            )

        return data

    @staticmethod
    @abstractmethod
    def _band_category():
        pass
