from business.models import Business
from common.utils import chunked_iterator
from core.celery import APP
from django.forms import model_to_dict
from fees.handlers.fee_creation_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fees.models import GeneralFee


@APP.task()
def apply_fees_to_all_businesses(product, provider):
    filters = {"product": product}
    if provider:
        filters["provider"] = provider

    print(filters)
    general_fees = GeneralFee.objects.filter(**filters).prefetch_related(
        "general_fee_bands"
    )

    if not general_fees.exists():
        return "General Fee has not been set"

    for gf in general_fees:
        bands = list(gf.general_fee_bands.all().values())
        base_data = model_to_dict(gf)

        for chunk in chunked_iterator(Business.objects.only("id").iterator(), 50):
            for business in chunk:
                payload = {
                    **base_data,
                    "business": business,
                    "business_fee_bands": bands,
                }
                FeeHandler().create_business_fee(payload)
