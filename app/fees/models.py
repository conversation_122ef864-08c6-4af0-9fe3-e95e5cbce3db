from decimal import Decimal

from common.enums import ProductEnum, ProviderEnum, VenderEnum
from common.models import AuditableModel
from django.db import models
from fees.enums import BandedFeeType, FeeType


class BusinessFee(AuditableModel):
    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="business_fee"
    )

    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    provider = models.CharField(
        choices=ProviderEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class BusinessFeeBand(AuditableModel):
    business_fee = models.ForeignKey(
        "fees.BusinessFee", on_delete=models.CASCADE, related_name="business_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)


class VenderFee(AuditableModel):
    vender = models.CharField(
        choices=VenderEnum.choices(), max_length=50, db_index=True
    )

    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    provider = models.CharField(
        choices=ProviderEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class VenderFeeBand(AuditableModel):
    vender_fee = models.ForeignKey(
        "fees.VenderFee", on_delete=models.CASCADE, related_name="vender_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)


class GeneralFee(AuditableModel):
    product = models.CharField(
        choices=ProductEnum.choices(), max_length=50, db_index=True
    )
    provider = models.CharField(
        choices=ProviderEnum.choices(), max_length=50, db_index=True
    )

    fee_type = models.CharField(max_length=20, choices=FeeType.choices())
    amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    cap_amount = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )


class GeneralFeeBand(AuditableModel):
    general_fee = models.ForeignKey(
        "fees.GeneralFee", on_delete=models.CASCADE, related_name="general_fee_bands"
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    fee_type = models.CharField(
        max_length=20,
        choices=BandedFeeType.choices(),
        default=BandedFeeType.Fixed.value,
    )

    lower_bound = models.DecimalField(max_digits=12, decimal_places=2)
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, default=Decimal("0")
    )
    upper_bound_infinite = models.BooleanField(default=False)
