from common.enums import CustomEnum


class TransactionClassEnum(CustomEnum):
    VIRTUAL_ACCOUNT = "VIRTUAL_ACCOUNT"
    TRANSFER = "TRANSFER"
    AIRTIME = "AIRTIME"
    DATA = "DATA"
    BETTING = "BETTING"
    ELECTRICITY = "ELECTRICITY"
    CABLE_TV = "CABLE_TV"
    SME_DATA = "SME_DATA"
    KYC = "KYC"
    EDUCATION = "EDUCATION"
    EPIN = "EPIN"
    RECURRING_DEBIT = "RECURRING_DEBIT"


class TransactionModeEnum(CustomEnum):
    CREDIT = "CREDIT"
    DEBIT = "DEBIT"


class TransactionStatusEnum(CustomEnum):
    PENDING = "PENDING"
    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"
