from django.db import models
from transaction.models.base import VASTransaction


class FundsTransferVasTransaction(VASTransaction):

    # Recipient details
    recipient_account_number = models.Char<PERSON><PERSON>(max_length=10)
    recipient_account_name = models.Char<PERSON><PERSON>(max_length=255)
    recipient_bank_name = models.Char<PERSON>ield(max_length=255)
    recipient_bank_code = models.Char<PERSON>ield(max_length=6)

    session_id = models.Char<PERSON>ield(max_length=40, db_index=True, null=True, blank=True)
    transaction_id = models.CharField(
        max_length=40, db_index=True, null=True, blank=True
    )

    # requery
    requery_response_code = models.Char<PERSON><PERSON>(
        max_length=5, help_text="Response for Requery", null=True
    )
    requery_response_message = models.Char<PERSON><PERSON>(
        max_length=5, help_text="requery_response_message", null=True
    )
    requery_response = models.JSONField(null=True)
    requery_retries_count = models.IntegerField(default=0)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "Funds Transfer Transaction"
        verbose_name_plural = "Funds Transfer Transactions"

    def __str__(self):
        return f"{self.business.name} - {self.session_id} - {self.recipient_account_number}"
