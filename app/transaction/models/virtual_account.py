from django.db import models
from transaction.models.base import VASTransaction


class VirtualAccountVasTransaction(VASTransaction):
    session_id = models.Char<PERSON>ield(max_length=40, db_index=True)

    # Source details
    source_account_number = models.Char<PERSON>ield(max_length=10)
    source_account_name = models.Char<PERSON>ield(max_length=255)
    source_bank_name = models.CharField(max_length=255)
    source_bank_code = models.Char<PERSON>ield(max_length=6)

    # Recipient details
    recipient_account_number = models.Char<PERSON>ield(max_length=10)
    recipient_account_name = models.Char<PERSON>ield(max_length=255)
    recipient_bank_name = models.Char<PERSON><PERSON>(max_length=255)
    recipient_bank_code = models.Char<PERSON>ield(max_length=6)

    # notification
    beneficiary_notified = models.BooleanField(default=False)
    notification_response = models.J<PERSON><PERSON>ield(null=True)
    notification_retry_count = models.IntegerField(default=0)

    # requery
    requery_response_code = models.Char<PERSON><PERSON>(
        max_length=5, help_text="Response for Requery", null=True
    )
    requery_response_message = models.Cha<PERSON><PERSON><PERSON>(
        max_length=5, help_text="requery_response_message", null=True
    )
    requery_response = models.JSONField(null=True)
    requery_retries_count = models.IntegerField(default=0)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "Virtual Account VAS Transaction"
        verbose_name_plural = "Virtual Account VAS Transactions"

    def __str__(self):
        return f"{self.business.name} - {self.session_id} - {self.recipient_account_number}"
