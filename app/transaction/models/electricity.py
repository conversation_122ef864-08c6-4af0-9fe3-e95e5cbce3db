from common.enums import ElectricityDiscoEnum, ElectricityDiscoTypeEnum
from django.db import models

from .base import VASTransaction


class ElectricityVASTransaction(VASTransaction):
    meter_number = models.CharField(max_length=20)
    biller = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=20)
    disco = models.CharField(max_length=20, choices=ElectricityDiscoEnum.choices())
    disco_type = models.CharField(
        max_length=20, choices=ElectricityDiscoTypeEnum.choices()
    )

    class Meta:
        db_table = "electricity_vas_transaction"
        verbose_name = "Electricity VAS Transaction"
        verbose_name_plural = "Electricity VAS Transactions"

    def __str__(self):
        return f"{self.meter_number} - {self.biller}"
