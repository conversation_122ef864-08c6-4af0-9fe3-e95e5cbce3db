from common.enums import CableTVBillerEnum
from common.models import AuditableModel
from django.db import models

from .base import VASTransaction


class CableTVVASTransaction(VASTransaction):
    biller = models.CharField(
        max_length=20, choices=CableTVBillerEnum.choices(), db_index=True
    )
    iuc_number = models.CharField(max_length=20)
    plan_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = "cable_tv_vas_transaction"
        verbose_name = "Cable TV VAS Transaction"
        verbose_name_plural = "Cable TV VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.biller} - {self.iuc_number}"


class CentralCableTvPlan(AuditableModel):
    provider = models.CharField(
        max_length=50, choices=CableTVBillerEnum.choices(), db_index=True
    )
    code = models.Char<PERSON>ield(max_length=100, db_index=True)
    name = models.Char<PERSON>ield(max_length=150)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    duration = models.CharField(max_length=30)

    objects = models.Manager()

    def __str__(self):
        return f"{self.name} - ({self.provider})"

    class Meta:
        ordering = ("provider",)
        verbose_name = "Central Cable TV Plan"
        verbose_name_plural = "Central Cable TV Plans"
