from common.enums import KYCTypeEnum
from django.db import models

from .base import VASTransaction


class KYCVASTransaction(VASTransaction):
    type = models.CharField(max_length=20, choices=KYCTypeEnum.choices(), db_index=True)

    class Meta:
        db_table = "kyc_vas_transaction"
        verbose_name = "KYC VAS Transaction"
        verbose_name_plural = "KYC VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.type}"
