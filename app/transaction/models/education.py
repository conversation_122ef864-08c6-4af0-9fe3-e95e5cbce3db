from common.enums import (
    EducationProviderEnum,
    EducationServiceTypeEnum,
    JambExamTypeEnum,
    WaecExamTypeEnum,
)
from django.db import models

from .base import VASTransaction


class EducationVASTransaction(VASTransaction):
    provider = models.CharField(
        max_length=20, choices=EducationProviderEnum.choices, db_index=True
    )
    service_type = models.CharField(
        max_length=20, choices=EducationServiceTypeEnum.choices, db_index=True
    )
    waec_exam_type = models.Char<PERSON>ield(
        max_length=20,
        choices=WaecExamTypeEnum.choices,
        db_index=True,
        null=True,
        blank=True,
    )
    jamb_exam_type = models.Char<PERSON>ield(
        max_length=20,
        choices=JambExamTypeEnum.choices,
        db_index=True,
        null=True,
        blank=True,
    )
    number_of_pins = models.IntegerField(null=True, blank=True)
    candidate_name = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    candidate_profile_code = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    # reference_number = models.CharField(max_length=20)pin,candidate_name, candidate_profile_code

    class Meta:
        db_table = "education_vas_transaction"
        verbose_name = "Education VAS Transaction"
        verbose_name_plural = "Education VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.provider}"
