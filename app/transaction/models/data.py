from common.enums import DataNetworkEnum
from django.db import models

from .base import VASTransaction


class DataVASTransaction(VASTransaction):
    network = models.CharField(
        max_length=20, choices=DataNetworkEnum.choices, db_index=True
    )
    phone_number = models.CharField(max_length=20)
    data_code = models.CharField(max_length=50)

    class Meta:
        db_table = "data_vas_transaction"
        verbose_name = "Data VAS Transaction"
        verbose_name_plural = "Data VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.phone_number} - {self.data_code}"
