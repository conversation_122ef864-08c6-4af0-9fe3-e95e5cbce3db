from common.enums import EpinAmount, EpinNetwork
from django.db import models

from .base import VASTransaction


class EpinVASTransaction(VASTransaction):
    network = models.CharField(
        max_length=20, choices=EpinNetwork.choices(), db_index=True
    )  # e.g. MTN, GLO, etc.
    quantity = models.IntegerField()
    epin_amount = models.IntegerField(
        choices=EpinAmount.choices(), db_index=True
    )  # e.g. 100, 200, 500, 1000

    class Meta:
        db_table = "epin_vas_transaction"
        verbose_name = "Epin VAS Transaction"
        verbose_name_plural = "Epin VAS Transactions"
