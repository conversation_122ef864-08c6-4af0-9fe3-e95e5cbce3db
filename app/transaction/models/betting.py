from common.enums import BetBillerEnum
from django.db import models

from .base import VASTransaction


class BettingVASTransaction(VASTransaction):
    biller = models.CharField(
        max_length=20, choices=BetBillerEnum.choices(), db_index=True
    )
    customer_id = models.CharField(max_length=50)

    class Meta:
        db_table = "betting_vas_transaction"
        verbose_name = "Betting VAS Transaction"
        verbose_name_plural = "Betting VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.biller} - {self.customer_id}"
