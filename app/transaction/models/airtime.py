from common.enums import AirtimeNetworkEnum
from django.db import models

from .base import VASTransaction


class AirtimeVASTransaction(VASTransaction):
    network = models.CharField(
        max_length=20, choices=AirtimeNetworkEnum.choices, db_index=True
    )  # e.g. MTN, GLO, etc.
    phone_number = models.CharField(max_length=20)

    class Meta:
        db_table = "airtime_vas_transaction"
        verbose_name = "Airtime VAS Transaction"
        verbose_name_plural = "Airtime VAS Transactions"

    def __str__(self):
        return f"{self.reference} - {self.phone_number} - {self.network}"
