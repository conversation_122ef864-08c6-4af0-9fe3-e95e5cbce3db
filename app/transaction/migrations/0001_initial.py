# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.enums
import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0001_initial"),
        ("wallet", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CentralCableTvPlan",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("DSTV", "DSTV"),
                            ("GOTV", "GOTV"),
                            ("STARTIMES", "STARTIMES"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("code", models.CharField(db_index=True, max_length=100)),
                ("name", models.CharField(max_length=150)),
                ("price", models.DecimalField(decimal_places=2, max_digits=12)),
                ("duration", models.CharField(max_length=30)),
            ],
            options={
                "verbose_name": "Central Cable TV Plan",
                "verbose_name_plural": "Central Cable TV Plans",
                "ordering": ("provider",),
            },
        ),
        migrations.CreateModel(
            name="AirtimeVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=common.enums.AirtimeNetworkEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("phone_number", models.CharField(max_length=20)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Airtime VAS Transaction",
                "verbose_name_plural": "Airtime VAS Transactions",
                "db_table": "airtime_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="BettingVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "biller",
                    models.CharField(
                        choices=[
                            ("Bet9ja", "Bet9ja"),
                            ("BangBet", "BangBet"),
                            ("SupaBet", "SupaBet"),
                            ("CloudBet", "CloudBet"),
                            ("BetLion", "BetLion"),
                            ("1xBet", "1xBet"),
                            ("MerryBet", "MerryBet"),
                            ("BetWay", "BetWay"),
                            ("BetLand", "BetLand"),
                            ("BetKing", "BetKing"),
                            ("LiveScoreBet", "LiveScoreBet"),
                            ("NaijaBet", "NaijaBet"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("customer_id", models.CharField(max_length=50)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Betting VAS Transaction",
                "verbose_name_plural": "Betting VAS Transactions",
                "db_table": "betting_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="CableTVVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "biller",
                    models.CharField(
                        choices=[
                            ("DSTV", "DSTV"),
                            ("GOTV", "GOTV"),
                            ("STARTIMES", "STARTIMES"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("iuc_number", models.CharField(max_length=20)),
                ("plan_code", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Cable TV VAS Transaction",
                "verbose_name_plural": "Cable TV VAS Transactions",
                "db_table": "cable_tv_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="DataVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=common.enums.DataNetworkEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("phone_number", models.CharField(max_length=20)),
                ("data_code", models.CharField(max_length=50)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Data VAS Transaction",
                "verbose_name_plural": "Data VAS Transactions",
                "db_table": "data_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="EducationVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "provider",
                    models.CharField(
                        choices=common.enums.EducationProviderEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "service_type",
                    models.CharField(
                        choices=common.enums.EducationServiceTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "waec_exam_type",
                    models.CharField(
                        blank=True,
                        choices=common.enums.WaecExamTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "jamb_exam_type",
                    models.CharField(
                        blank=True,
                        choices=common.enums.JambExamTypeEnum.choices,
                        db_index=True,
                        max_length=20,
                        null=True,
                    ),
                ),
                ("number_of_pins", models.IntegerField(blank=True, null=True)),
                (
                    "candidate_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "candidate_profile_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Education VAS Transaction",
                "verbose_name_plural": "Education VAS Transactions",
                "db_table": "education_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="ElectricityVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("meter_number", models.CharField(max_length=20)),
                ("biller", models.CharField(max_length=50)),
                ("phone_number", models.CharField(max_length=20)),
                (
                    "disco",
                    models.CharField(
                        choices=[
                            ("AbujaElectric", "AbujaElectric"),
                            ("BeninElectric", "BeninElectric"),
                            ("EnuguElectric", "EnuguElectric"),
                            ("EkoElectric", "EkoElectric"),
                            ("IbadanElectric", "IbadanElectric"),
                            ("IkejaElectric", "IkejaElectric"),
                            ("JosElectric", "JosElectric"),
                            ("PortharcourtElectric", "PortharcourtElectric"),
                            ("KadunaElectric", "KadunaElectric"),
                            ("KanoElectric", "KanoElectric"),
                            ("YolaElectric", "YolaElectric"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "disco_type",
                    models.CharField(
                        choices=[("PREPAID", "PREPAID"), ("POSTPAID", "POSTPAID")],
                        max_length=20,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Electricity VAS Transaction",
                "verbose_name_plural": "Electricity VAS Transactions",
                "db_table": "electricity_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="EpinVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "network",
                    models.CharField(
                        choices=[
                            ("MTN", "MTN"),
                            ("GLO", "GLO"),
                            ("AIRTEL", "AIRTEL"),
                            ("9MOBILE", "9MOBILE"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("quantity", models.IntegerField()),
                (
                    "epin_amount",
                    models.IntegerField(
                        choices=[(100, 100), (200, 200), (500, 500), (1000, 1000)],
                        db_index=True,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Epin VAS Transaction",
                "verbose_name_plural": "Epin VAS Transactions",
                "db_table": "epin_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="FundsTransferVasTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("recipient_account_number", models.CharField(max_length=10)),
                ("recipient_account_name", models.CharField(max_length=255)),
                ("recipient_bank_name", models.CharField(max_length=255)),
                ("recipient_bank_code", models.CharField(max_length=6)),
                (
                    "session_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "requery_response_code",
                    models.CharField(
                        help_text="Response for Requery", max_length=5, null=True
                    ),
                ),
                (
                    "requery_response_message",
                    models.CharField(
                        help_text="requery_response_message", max_length=5, null=True
                    ),
                ),
                ("requery_response", models.JSONField(null=True)),
                ("requery_retries_count", models.IntegerField(default=0)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Funds Transfer Transaction",
                "verbose_name_plural": "Funds Transfer Transactions",
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="KYCVASTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("BVN", "BVN"),
                            ("NIN", "NIN"),
                            ("PHONE_NUMBER", "PHONE_NUMBER"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "KYC VAS Transaction",
                "verbose_name_plural": "KYC VAS Transactions",
                "db_table": "kyc_vas_transaction",
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                ("merchant_reference", models.CharField(db_index=True, max_length=100)),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                (
                    "txn_class",
                    models.CharField(
                        choices=transaction.enums.TransactionClassEnum.choices,
                        db_index=True,
                        max_length=30,
                    ),
                ),
                ("type", models.CharField(db_index=True, max_length=50)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                (
                    "is_wallet_impacted",
                    models.BooleanField(db_index=True, default=True),
                ),
                (
                    "vender",
                    models.CharField(
                        blank=True,
                        choices=common.enums.VenderEnum.choices,
                        db_index=True,
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="transactions",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CommissionTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "source_transaction_reference",
                    models.CharField(db_index=True, max_length=100),
                ),
                ("reference", models.CharField(db_index=True, max_length=100)),
                (
                    "txn_class",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        max_length=30,
                    ),
                ),
                ("narration", models.TextField()),
                (
                    "amount",
                    models.DecimalField(
                        db_index=True, decimal_places=2, default=0, max_digits=20
                    ),
                ),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="wallet.wallet",
                    ),
                ),
                (
                    "source_transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="transaction.transaction",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="VirtualAccountVasTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("session_id", models.CharField(db_index=True, max_length=40)),
                ("source_account_number", models.CharField(max_length=10)),
                ("source_account_name", models.CharField(max_length=255)),
                ("source_bank_name", models.CharField(max_length=255)),
                ("source_bank_code", models.CharField(max_length=6)),
                ("recipient_account_number", models.CharField(max_length=10)),
                ("recipient_account_name", models.CharField(max_length=255)),
                ("recipient_bank_name", models.CharField(max_length=255)),
                ("recipient_bank_code", models.CharField(max_length=6)),
                ("beneficiary_notified", models.BooleanField(default=False)),
                ("notification_response", models.JSONField(null=True)),
                ("notification_retry_count", models.IntegerField(default=0)),
                (
                    "requery_response_code",
                    models.CharField(
                        help_text="Response for Requery", max_length=5, null=True
                    ),
                ),
                (
                    "requery_response_message",
                    models.CharField(
                        help_text="requery_response_message", max_length=5, null=True
                    ),
                ),
                ("requery_response", models.JSONField(null=True)),
                ("requery_retries_count", models.IntegerField(default=0)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Account VAS Transaction",
                "verbose_name_plural": "Virtual Account VAS Transactions",
                "ordering": ("-created_at",),
            },
        ),
    ]
