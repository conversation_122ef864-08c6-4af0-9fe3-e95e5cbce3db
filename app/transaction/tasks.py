from business.models import Business
from core.celery import APP
from fees.handlers.credit_commission_handler import ProcessCommissionHandler
from transaction.models import Transaction


@APP.task()
def process_commission(txn_id, business_id):
    txn = Transaction.objects.filter(pk=txn_id).first()
    if not txn:
        return "Invalid transaction"

    business = Business.objects.filter(pk=business_id).first()
    if not business:
        return "Invalid business"

    ProcessCommissionHandler().handle(txn, business)
