from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.cable_tv import CableTVVASTransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class CableTVVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> CableTVVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return CableTVVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: CableTVVASTransaction, update_fields: dict
    ) -> CableTVVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.CABLE_TV.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
