from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.electricity import ElectricityVASTransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class ElectricityVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> ElectricityVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return ElectricityVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: ElectricityVASTransaction, update_fields: dict
    ) -> ElectricityVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.ELECTRICITY.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
