from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.kyc import KYC<PERSON>STransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class KYCVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> KYCVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return KYCVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: KYCVASTransaction, update_fields: dict
    ) -> KYCVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.KYC.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
