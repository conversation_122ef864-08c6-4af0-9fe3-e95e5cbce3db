from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction

from ..models.transfer import FundsTransferVasTransaction
from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class FundsTransferTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> FundsTransferVasTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return FundsTransferVasTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: FundsTransferVasTransaction, update_fields: dict
    ) -> FundsTransferVasTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.TRANSFER.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
