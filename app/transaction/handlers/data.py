from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.data import DataVASTransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class DataVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> DataVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return DataVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: DataVASTransaction, update_fields: dict
    ) -> DataVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.DATA.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
