from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.education import EducationVASTransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class EducationVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> EducationVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return EducationVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: EducationVASTransaction, update_fields: dict
    ) -> EducationVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.EDUCATION.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
