from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.base import Transaction
from transaction.models.epin import EpinVASTransaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class EpinVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> EpinVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return EpinVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: EpinVASTransaction, update_fields: dict
    ) -> EpinVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.EPIN.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
