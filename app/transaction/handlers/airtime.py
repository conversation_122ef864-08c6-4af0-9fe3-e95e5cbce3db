from ledger.models import LedgerTransaction, LedgerTypeEnum
from transaction.models.airtime import AirtimeVASTransaction
from transaction.models.base import Transaction

from ..utils import get_ledger
from .interface import BaseVASTransactionHandler


class AirtimeVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> AirtimeVASTransaction:
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return AirtimeVASTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, vas_txn: AirtimeVASTransaction, update_fields: dict
    ) -> AirtimeVASTransaction:
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        ledger = get_ledger(LedgerTypeEnum.AIRTIME.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn
