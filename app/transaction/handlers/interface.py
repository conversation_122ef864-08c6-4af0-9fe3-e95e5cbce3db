from abc import ABC, abstractmethod
from typing import Any

from common.enums import CoreServiceResponseStatus
from ledger.models import LedgerTransaction
from transaction.enums import TransactionStatusEnum
from transaction.models.base import Transaction


class BaseVASTransactionHandler(ABC):
    @abstractmethod
    def create_vas_transaction(self, txn: Transaction, extra_fields: dict) -> Any:
        pass

    @abstractmethod
    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        pass

    @abstractmethod
    def update_vas_transaction(self, vas_txn: Any, update_fields: dict) -> Any:
        status = update_fields.pop("status")
        match status:
            case CoreServiceResponseStatus.Pending.value:
                status = TransactionStatusEnum.PENDING.value
            case CoreServiceResponseStatus.Success.value:
                status = TransactionStatusEnum.SUCCESSFUL.value
            case CoreServiceResponseStatus.Failed.value:
                status = TransactionStatusEnum.FAILED.value

        vas_txn.status = status
        for key, value in update_fields.items():
            setattr(vas_txn, key, value)
        vas_txn.save()
        return vas_txn

    def get_base_payload(self, txn: Transaction) -> dict:
        return {
            "wallet": txn.wallet,
            "business": txn.business,
            "reference": txn.reference,
            "merchant_reference": txn.merchant_reference,
            "status": txn.status,
            "mode": txn.mode,
            "amount": txn.amount,
            "charge": txn.charge,
            "net_amount": txn.net_amount,
            "narration": txn.narration,
        }
