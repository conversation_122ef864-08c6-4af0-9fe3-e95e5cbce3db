from typing import Any

from ledger.models import LedgerTransaction
from transaction.handlers.interface import Base<PERSON><PERSON>ransactionHandler
from transaction.models import Transaction


class VirtualAccountVASTransactionHandler(BaseVASTransactionHandler):
    def create_vas_transaction(self, txn: Transaction, extra_fields: dict) -> Any:
        pass

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        pass

    def update_vas_transaction(self, vas_txn: Any, update_fields: dict) -> Any:
        pass
