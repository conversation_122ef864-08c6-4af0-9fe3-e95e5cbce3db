from account.enums import VA_PROVIDER_CHOICES
from account.managers import AccountPoolManager
from common.models import AuditableModel
from django.db import models


class AccountPool(AuditableModel):
    objects = AccountPoolManager()
    provider = models.CharField(max_length=30, choices=VA_PROVIDER_CHOICES)
    account_number = models.CharField(max_length=100, db_index=True, unique=True)
    is_used = models.BooleanField(default=False, db_index=True)
    batch_id = models.IntegerField(default=0)

    class Meta:
        unique_together = ("account_number", "provider")

    def __str__(self):
        return f"{self.account_number}"
