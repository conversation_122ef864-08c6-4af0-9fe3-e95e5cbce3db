from collections import Counter

from account.models import AccountPool
from django.core.management.base import BaseCommand
from django.db import transaction


class Command(BaseCommand):
    """Usage --> python manage.py mark_used_accounts"""

    help = "Mark AccountPool.is_used=True for accounts found in Wallet or repeated digits in AccountPool.account_number"

    def add_arguments(self, parser):
        parser.add_argument(
            "--chunk_size",
            type=int,
            default=10000,
            help="Number of records to process per chunk",
        )

    def __mark_repetitive_accounts(self, *args, **options):
        chunk_size = options["chunk_size"]

        qs = AccountPool.objects.filter(is_used=False).values("id", "account_number")
        total_checked = 0
        total_marked = 0
        buffer = []

        for row in qs.iterator(chunk_size):
            acc_id = row["id"]
            acc_num = row["account_number"]
            digit_count = Counter(acc_num)

            if any(count >= 5 for count in digit_count.values()):
                buffer.append(acc_id)

            if len(buffer) >= chunk_size:
                with transaction.atomic():
                    AccountPool.objects.filter(id__in=buffer).update(is_used=True)
                total_marked += len(buffer)
                buffer = []

            total_checked += 1
            if total_checked % 10000 == 0:
                self.stdout.write(f"Checked {total_checked} accounts...")

        # Final flush
        if buffer:
            with transaction.atomic():
                AccountPool.objects.filter(id__in=buffer).update(is_used=True)
            total_marked += len(buffer)

        self.stdout.write(
            self.style.SUCCESS(
                f"Checked {total_checked} accounts. Marked {total_marked} as used due to repetitive digits."
            )
        )

    # def __mark_used_accounts(self, *args, **options):
    #     chunk_size = options["chunk_size"]
    #
    #     # Step 1: Get all kolomoni accounts in use
    #     wallet_accounts = set(Wallet.objects.values_list("kolomoni_account", flat=True))
    #     self.stdout.write(f"Found {len(wallet_accounts)} wallet accounts to check.")
    #
    #     # Step 2: Find AccountNumbers that match and are not already marked as used
    #     matching_accounts = AccountPool.objects.filter(
    #         account_number__in=wallet_accounts, is_used=False
    #     ).values_list("pk", flat=True)
    #
    #     total_to_update = matching_accounts.count()
    #     self.stdout.write(f"Marking {total_to_update} account numbers as used...")
    #
    #     # Step 3: Batch update
    #     updated = 0
    #     for i in range(0, total_to_update, chunk_size):
    #         pks = matching_accounts[i : i + chunk_size]
    #         with transaction.atomic():
    #             AccountPool.objects.filter(pk__in=pks).update(is_used=True)
    #         updated += len(pks)
    #         self.stdout.write(f"Updated {updated}/{total_to_update}...")
    #
    #     self.stdout.write(
    #         self.style.SUCCESS(f"Done. Marked {updated} account numbers as used.")
    #     )

    def handle(self, *args, **options):
        self.__mark_repetitive_accounts(*args, **options)
        # self.__mark_used_accounts(*args, **options)
