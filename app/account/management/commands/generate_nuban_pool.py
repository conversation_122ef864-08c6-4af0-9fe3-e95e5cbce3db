from account.models import AccountPool
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """Usage --> python manage.py generate_nuban_pool 0"""

    help = "Efficiently generate a pool of NUBAN account numbers per batch"

    def add_arguments(self, parser):
        parser.add_argument(
            "initial_batch_id",
            type=int,
            help="Starting batch ID (will auto-increment if taken)",
        )
        parser.add_argument(
            "--chunk_size",
            type=int,
            default=50000,
            help="Number of records to insert per bulk insert",
        )

    def handle(self, *args, **options):
        batch_id = options["initial_batch_id"]
        chunk_size = options["chunk_size"]

        # Auto-pick the next available batch
        original_batch_id = batch_id
        while AccountPool.objects.filter(batch_id=batch_id).exists():
            batch_id += 1
            if batch_id > 9:
                raise Exception(
                    "Batch ID has peaked at 9 already. Please try again with a different account pool."
                )

        if batch_id != original_batch_id:
            self.stdout.write(
                self.style.WARNING(
                    f"Batch {original_batch_id} already exists. Using next available batch: {batch_id}"
                )
            )

        prefix = f"551{batch_id}"[:4]  # E.g., 5510, 5511, etc.

        def account_generator():
            for i in range(1_000_000):  # 0 to 999999
                suffix = f"{i:06d}"
                full_account = f"{prefix}{suffix}"[:10]
                yield AccountPool(account_number=full_account, batch_id=batch_id)

        batch = []
        total_inserted = 0

        for account in account_generator():
            batch.append(account)
            if len(batch) >= chunk_size:
                AccountPool.objects.bulk_create(batch, ignore_conflicts=True)
                total_inserted += len(batch)
                self.stdout.write(f"Inserted {total_inserted} records...")
                batch = []

        if batch:
            AccountPool.objects.bulk_create(batch, ignore_conflicts=True)
            total_inserted += len(batch)

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully generated {total_inserted} account numbers for batch {batch_id}"
            )
        )
