from common.models import AuditableModel
from django.db import models
from django.db.models import F
from wallet.enums import WalletEnums


class Wallet(AuditableModel, WalletEnums):
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="wallets",
        db_index=True,
    )
    type = models.CharField(
        max_length=30, choices=WalletEnums.WALLET_TYPE_CHOICES, db_index=True
    )
    balance = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    account_number = models.CharField(max_length=30, blank=True, null=True)
    account_name = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        unique_together = ("business", "type")

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS <{self.business.owner.email}> -- {self.type} -- {self.balance:,.2f}"
        return f"{self.business.name} <{self.business.owner.email}> -- {self.type} -- {self.balance:,.2f}"

    def debit(self, amount):
        self.balance = F("balance") - amount
        self.save()
        self.refresh_from_db()

    def credit(self, amount):
        self.balance = F("balance") + amount
        self.save()
        self.refresh_from_db()
