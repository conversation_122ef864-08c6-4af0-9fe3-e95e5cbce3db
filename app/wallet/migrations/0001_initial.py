# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import wallet.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Wallet",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("GENERAL", "GENERAL"),
                            ("ADMIN", "ADMIN"),
                            ("COMMISSION", "COMMISSION"),
                            ("WEMA_VIRTUAL_ACCOUNT", "WEMA_VIRTUAL_ACCOUNT"),
                            ("ACCESS_VIRTUAL_ACCOUNT", "ACCESS_VIRTUAL_ACCOUNT"),
                            ("KOLOMONI_VIRTUAL_ACCOUNT", "KOLOMONI_VIRTUAL_ACCOUNT"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        db_index=True,
                        max_length=30,
                    ),
                ),
                (
                    "balance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("bank_name", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=30, null=True),
                ),
                (
                    "account_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="wallets",
                        to="business.business",
                    ),
                ),
            ],
            options={
                "unique_together": {("business", "type")},
            },
            bases=(models.Model, wallet.enums.WalletEnums),
        ),
    ]
