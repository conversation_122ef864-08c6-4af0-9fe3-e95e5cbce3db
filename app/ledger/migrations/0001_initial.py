# Generated by Django 5.1.7 on 2025-06-22 18:31

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("transaction", "0001_initial"),
        ("wallet", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ledger",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT_WEMA", "VIRTUAL_ACCOUNT_WEMA"),
                            ("VIRTUAL_ACCOUNT_KOLOMONI", "VIRTUAL_ACCOUNT_KOLOMONI"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                            ("AIRTIME_COMMISSION", "AIRTIME_COMMISSION"),
                            ("DATA_COMMISSION", "DATA_COMMISSION"),
                            ("EPIN_COMMISSION", "EPIN_COMMISSION"),
                            ("CABLE_TV_COMMISSION", "CABLE_TV_COMMISSION"),
                            ("EDUCATION_COMMISSION", "EDUCATION_COMMISSION"),
                            ("ELECTRIC_COMMISSION", "ELECTRIC_COMMISSION"),
                            ("BETTING_COMMISSION", "BETTING_COMMISSION"),
                        ],
                        db_index=True,
                        max_length=30,
                    ),
                ),
                ("balance", models.DecimalField(decimal_places=2, max_digits=20)),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="LedgerTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "ledger",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="ledger_transactions",
                        to="ledger.ledger",
                    ),
                ),
                (
                    "source_commission",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="source_commission_ledger_transactions",
                        to="transaction.commissiontransaction",
                    ),
                ),
                (
                    "source_transaction",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="source_txn_ledger_transactions",
                        to="transaction.transaction",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="wallet_ledger_transactions",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
    ]
