from core.celery import APP
from transaction.models import Transaction
from transaction.models.base import CommissionTransaction
from transaction.utils import get_ledger


@APP.task()
def credit_ledger(ledger: str, txn_id=None, comm_txn_id=None):
    return _process_ledger_action("credit", ledger, txn_id, comm_txn_id)


@APP.task()
def debit_ledger(ledger: str, txn_id=None, comm_txn_id=None):
    return _process_ledger_action("debit", ledger, txn_id, comm_txn_id)


def _process_ledger_action(
    action: str, ledger_name: str, txn_id=None, comm_txn_id=None
):
    ledger = get_ledger(ledger_name)

    if txn_id:
        txn = Transaction.objects.filter(pk=txn_id).first()
        if not txn:
            return (
                f"{action.title()} ledger error: Transaction does not exist: {txn_id}"
            )

        getattr(ledger, action)(txn=txn)

        return None

    if comm_txn_id:
        comm_txn = CommissionTransaction.objects.filter(pk=comm_txn_id).first()
        if not comm_txn:
            return f"{action.title()} ledger error: Commission Transaction does not exist: {comm_txn_id}"

        getattr(ledger, action)(comm_txn=comm_txn)

        return None
