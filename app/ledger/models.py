from common.models import AuditableModel
from django.db import models, transaction
from django.db.models import F
from ledger.enums import LedgerTypeEnum
from transaction.models.base import CommissionTransaction, Transaction


class Ledger(AuditableModel):
    type = models.CharField(
        max_length=30, choices=LedgerTypeEnum.choices(), db_index=True
    )
    balance = models.DecimalField(max_digits=20, decimal_places=2)

    def credit(
        self, txn: Transaction = None, comm_txn: CommissionTransaction = None
    ) -> "LedgerTransaction":
        with transaction.atomic():
            old_balance = self.balance
            item = comm_txn or txn

            self.balance = F("balance") + item.amount
            self.save()

            # Refresh from DB to get the updated numeric balance
            self.refresh_from_db(fields=["balance"])
            new_balance = self.balance

            wallet = item.wallet

            return LedgerTransaction.objects.create(
                ledger=self,
                amount=item.amount,
                narration=txn.narration,
                source_transaction=txn,
                source_commission=comm_txn,
                wallet=wallet,
                old_balance=old_balance,
                new_balance=new_balance,
            )

    def debit(
        self, txn: Transaction = None, comm_txn: CommissionTransaction = None
    ) -> "LedgerTransaction":
        with transaction.atomic():
            old_balance = self.balance
            item = comm_txn or txn

            self.balance = F("balance") - item.amount
            self.save()

            # Refresh to get the evaluated balance
            self.refresh_from_db(fields=["balance"])
            new_balance = self.balance

            wallet = item.wallet

            return LedgerTransaction.objects.create(
                ledger=self,
                amount=item.amount,
                narration=item.narration,
                source_transaction=txn,
                source_commission=comm_txn,
                wallet=wallet,
                old_balance=old_balance,
                new_balance=new_balance,
            )

    def __str__(self):
        return f"{self.type} -- {self.balance}"

    class Meta:
        ordering = ("-created_at",)


class LedgerTransaction(AuditableModel):
    ledger = models.ForeignKey(
        Ledger,
        on_delete=models.PROTECT,
        db_index=True,
        related_name="ledger_transactions",
    )
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="wallet_ledger_transactions",
    )

    amount = models.DecimalField(max_digits=20, decimal_places=2)
    narration = models.TextField()

    source_transaction = models.ForeignKey(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="source_txn_ledger_transactions",
        null=True,
        blank=True,
    )
    source_commission = models.ForeignKey(
        "transaction.CommissionTransaction",
        on_delete=models.PROTECT,
        db_index=True,
        related_name="source_commission_ledger_transactions",
        null=True,
        blank=True,
    )

    old_balance = models.DecimalField(max_digits=20, decimal_places=2)
    new_balance = models.DecimalField(max_digits=20, decimal_places=2)

    def __str__(self):
        txn = self.source_transaction or self.source_commission
        return f"{txn} -- {self.amount}"

    class Meta:
        ordering = ("-created_at",)
