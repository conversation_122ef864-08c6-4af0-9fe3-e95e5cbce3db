from decimal import Decimal

from django.core.management.base import BaseCommand
from ledger.enums import LedgerTypeEnum
from ledger.models import Ledger


class Command(BaseCommand):
    help = "Seed System VAS Ledgers"

    def handle(self, *args, **options):
        seeded = 0
        skipped = 0

        for ledger_type in LedgerTypeEnum:
            ledger, created = Ledger.objects.get_or_create(
                type=ledger_type.value,
                defaults={"balance": Decimal("0.00")},
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"Created ledger for: {ledger_type.value}")
                )
                seeded += 1
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Ledger already exists for: {ledger_type.value}"
                    )
                )
                skipped += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"Seeding complete. Created: {seeded}, Skipped: {skipped}"
            )
        )
